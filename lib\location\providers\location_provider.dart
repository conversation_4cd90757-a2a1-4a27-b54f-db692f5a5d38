// Konum provider'ı
// Bu provider, konum ile ilgili state yönetimini sağlar:
// - Mevcut konum bilgisi
// - Konum takibi durumu
// - Konum izin durumu
// - Konum servisi durumu
// - Kıble yönü bilgisi

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import '../models/location_model.dart';
import '../services/location_service.dart';

/// Konum provider sınıfı
class LocationProvider extends ChangeNotifier {
  /// Konum servisi örneği
  final LocationService _locationService;

  /// Mevcut konum bilgisi
  LocationModel? _currentLocation;

  /// Otomatik başlatma tamamlandı mı?
  bool _autoInitialized = false;

  /// Otomatik başlatma tamamlandı mı?
  bool get autoInitialized => _autoInitialized;

  /// Mevcut konum bilgisi
  LocationModel? get currentLocation => _currentLocation;

  /// Konum takibi aktif mi?
  bool _isTracking = false;

  /// Konum takibi aktif mi?
  bool get isTracking => _isTracking;

  /// Konum izin durumu
  LocationPermissionStatus? _permissionStatus;

  /// Konum izin durumu
  LocationPermissionStatus? get permissionStatus => _permissionStatus;

  /// Konum servisi durumu
  LocationServiceStatus? _serviceStatus;

  /// Konum servisi durumu
  LocationServiceStatus? get serviceStatus => _serviceStatus;

  /// Konum takibi akışı için subscription
  StreamSubscription<LocationModel>? _locationSubscription;

  /// Konum servisi durumu akışı için subscription
  StreamSubscription<LocationServiceStatus>? _serviceStatusSubscription;

  /// Hata mesajı
  String? _errorMessage;

  /// Hata mesajı
  String? get errorMessage => _errorMessage;

  /// Yükleniyor durumu
  bool _isLoading = false;

  /// Retry sayacı
  int _retryCount = 0;

  /// Retry sayacı
  int get retryCount => _retryCount;

  /// Yükleniyor durumu
  bool get isLoading => _isLoading;

  /// Konum provider oluşturucu
  LocationProvider({LocationService? locationService})
    : _locationService = locationService ?? LocationService() {
    _initialize();
  }

  /// Provider'ı başlatır
  Future<void> _initialize() async {
    await _checkServiceStatus();
    await _checkPermissionStatus();
    notifyListeners();
  }

  /// Konum servisi durumunu kontrol eder
  Future<void> _checkServiceStatus() async {
    try {
      _isLoading = true;
      notifyListeners();

      _serviceStatus = await _locationService.checkServiceStatus();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = 'Konum servisi durumu kontrol edilemedi: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Konum izin durumunu kontrol eder
  Future<void> _checkPermissionStatus() async {
    try {
      _permissionStatus = await _locationService.checkPermissionStatus();
      notifyListeners();
    } catch (e) {
      _errorMessage = 'Konum izin durumu kontrol edilemedi: $e';
      notifyListeners();
    }
  }

  /// Konum takibini başlatır
  Future<void> startLocationTracking() async {
    if (_isTracking) return;

    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // İzin durumunu kontrol et
      if (_permissionStatus != LocationPermissionStatus.granted) {
        await _requestPermission();
      }

      // Servis durumunu kontrol et
      if (_serviceStatus != LocationServiceStatus.enabled) {
        _errorMessage = 'Konum servisi devre dışı';
        _isLoading = false;
        notifyListeners();
        return;
      }

      // Konum takibini başlat
      final success = await _locationService.startLocationTracking(
        desiredAccuracy: LocationAccuracy.high,
        distanceFilter: 100,
      );

      if (success) {
        _isTracking = true;
        _listenToLocationStream();
        _listenToServiceStatusStream();
      } else {
        _errorMessage = 'Konum takibi başlatılamadı';
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = 'Konum takibi başlatılırken hata oluştu: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Konum takibini durdurur
  void stopLocationTracking() {
    if (!_isTracking) return;

    _isTracking = false;
    _locationSubscription?.cancel();
    _serviceStatusSubscription?.cancel();

    _locationService.stopLocationTracking();
    notifyListeners();
  }

  /// Tek seferlik konum bilgisi alır
  Future<void> getCurrentLocation() async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // İzin durumunu kontrol et
      if (_permissionStatus != LocationPermissionStatus.granted) {
        await _requestPermission();
      }

      // Servis durumunu kontrol et
      if (_serviceStatus != LocationServiceStatus.enabled) {
        _errorMessage = 'Konum servisi devre dışı';
        _isLoading = false;
        notifyListeners();
        return;
      }

      // Konum bilgisini al
      final location = await _locationService.getCurrentLocation();
      if (location != null) {
        _currentLocation = location;
        _calculateQiblaDirection(location);
        _getLocationNameAutomatically(location);
      } else {
        _errorMessage = 'Konum bilgisi alınamadı';
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = 'Konum bilgisi alınırken hata oluştu: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Konum izni ister
  Future<void> _requestPermission() async {
    try {
      _permissionStatus = await _locationService.requestPermission();
      notifyListeners();
    } catch (e) {
      _errorMessage = 'Konum izni istenirken hata oluştu: $e';
      notifyListeners();
    }
  }

  /// Konum akışını dinler
  void _listenToLocationStream() {
    _locationSubscription?.cancel();

    _locationSubscription = _locationService.locationStream?.listen(
      (location) {
        _currentLocation = location;
        _calculateQiblaDirection(location);
        _getLocationNameAutomatically(location);
        notifyListeners();
      },
      onError: (error) {
        _errorMessage = 'Konum akışında hata: $error';
        notifyListeners();
      },
    );
  }

  /// Konum servisi durum akışını dinler
  void _listenToServiceStatusStream() {
    _serviceStatusSubscription?.cancel();

    _serviceStatusSubscription = _locationService.serviceStatusStream?.listen(
      (status) {
        _serviceStatus = status;
        notifyListeners();
      },
      onError: (error) {
        _errorMessage = 'Konum servisi durumu akışında hata: $error';
        notifyListeners();
      },
    );
  }

  /// Kıble yönünü hesaplar
  void _calculateQiblaDirection(LocationModel location) async {
    try {
      final qiblaDirection = await _locationService.calculateQiblaDirection(
        location.latitude,
        location.longitude,
      );
      _currentLocation = _currentLocation?.copyWith(
        qiblaDirection: qiblaDirection,
      );
    } catch (e) {
      _errorMessage = 'Kıble yönü hesaplanamadı: $e';
    }
  }

  /// Konum adını otomatik olarak alır
  void _getLocationNameAutomatically(LocationModel location) async {
    try {
      final fullAddress = await _locationService.getFullAddress(
        location.latitude,
        location.longitude,
      );

      if (fullAddress != null) {
        _currentLocation = _currentLocation?.copyWith(
          locationName: fullAddress, // Kısa isim için full kullan
          fullAddress: fullAddress,
        );
      }
    } catch (e) {
      // Konum adı alınamazsa hata mesajı gösterme, sadece logla
      debugPrint('Konum adresi alınamadı: $e');
    }
  }

  /// Konum adını alır (full address)
  Future<String?> getLocationName() async {
    if (_currentLocation == null) return null;

    try {
      final fullAddress = await _locationService.getFullAddress(
        _currentLocation!.latitude,
        _currentLocation!.longitude,
      );

      if (fullAddress != null) {
        _currentLocation = _currentLocation?.copyWith(
          locationName: fullAddress,
          fullAddress: fullAddress,
        );
        notifyListeners();
      }

      return fullAddress;
    } catch (e) {
      // Sessiz hata: Sadece logla, UI'ye yansıtma
      debugPrint('Konum adresi alınamadı: $e');
      return null;
    }
  }

  /// Adresten konum bulur
  Future<LocationModel?> getLocationFromAddress(String address) async {
    try {
      _isLoading = true;
      notifyListeners();

      final location = await _locationService.getLocationFromAddress(address);
      if (location != null) {
        _currentLocation = location;
        _calculateQiblaDirection(location);
      }

      _isLoading = false;
      notifyListeners();

      return location;
    } catch (e) {
      _errorMessage = 'Adresten konum bulunamadı: $e';
      _isLoading = false;
      notifyListeners();
      return null;
    }
  }

  /// Hata mesajını temizler
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Otomatik konum başlatma
  /// Son bilinen konumu yükler, izinleri kontrol eder, güncel konumu alır ve takibi başlatır.
  Future<void> initializeWithLocation({bool retryOnFailure = false}) async {
    if (_autoInitialized) return;

    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // 1. Son bilinen konumu al ve ata (eğer mevcutsa)
      final lastKnownLocation = await _locationService.getLastKnownLocation();
      if (lastKnownLocation != null) {
        _currentLocation = lastKnownLocation;
      }

      // 2. İzin ve servis durumlarını kontrol et
      await _checkPermissionStatus();
      await _checkServiceStatus();

      // 3. Eğer izin yoksa izin iste
      if (_permissionStatus != LocationPermissionStatus.granted) {
        await _requestPermission();
        // İzin sonrası tekrar kontrol et
        await _checkPermissionStatus();
        if (_permissionStatus != LocationPermissionStatus.granted) {
          _errorMessage = 'Konum izni alınamadı, otomatik başlatma durduruldu.';
          _isLoading = false;
          notifyListeners();
          return;
        }
      }

      // Servis zaten kontrol edildi, ancak getCurrentLocation'da tekrar doğrulanır

      // 4. Güncel konumu al
      await getCurrentLocation();
      if (_errorMessage != null) {
        if (retryOnFailure) {
          final retrySuccess = await retryLocationRequest();
          if (!retrySuccess) {
            _isLoading = false;
            notifyListeners();
            return;
          }
          // Retry başarılı, devam et
        } else {
          _isLoading = false;
          notifyListeners();
          return;
        }
      }

      // Konum adresi al (otomatik reverse geocoding)
      await getLocationName();

      // 5. Konum takibini başlat
      await startLocationTracking();
      if (_errorMessage != null) {
        _isLoading = false;
        notifyListeners();
        return;
      }

      // 6. Başarılı tamamlandı
      _autoInitialized = true;
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = 'Otomatik konum başlatma sırasında hata oluştu: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Retry konum isteği yapar
  /// Maksimum 3 retry ile konum alımını dener.
  Future<bool> retryLocationRequest() async {
    if (_retryCount >= 3) {
      _errorMessage =
          'Maksimum retry sayısına ulaşıldı, lütfen ayarları kontrol edin';
      notifyListeners();
      return false;
    }

    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      await getCurrentLocation();
      if (_currentLocation != null) {
        _retryCount = 0;
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        throw Exception('Konum alınamadı');
      }
    } catch (e) {
      _retryCount++;
      _errorMessage = 'Retry $_retryCount/3: $e';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  /// Provider'ı temizler
  @override
  void dispose() {
    _locationSubscription?.cancel();
    _serviceStatusSubscription?.cancel();
    stopLocationTracking();
    super.dispose();
  }
}
