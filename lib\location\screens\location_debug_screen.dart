// Konum debug ekranı
// Bu ekran, konum servisi ve provider'ını test etmek için kullanılır:
// - Mevcut konum bilgisi gösterimi
// - Konum takibi kontrolü
// - Konum izin durumu
// - Konum servisi durumu
// - Kıble yönü bilgisi

import 'package:flutter/material.dart';
import '../providers/location_provider.dart';
import '../models/location_model.dart';

/// Konum debug ekranı widget'ı
class LocationDebugScreen extends StatefulWidget {
  /// Konum provider'ı
  final LocationProvider locationProvider;

  /// Konum debug ekranı oluşturucu
  const LocationDebugScreen({super.key, required this.locationProvider});

  @override
  State<LocationDebugScreen> createState() => _LocationDebugScreenState();
}

class _LocationDebugScreenState extends State<LocationDebugScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Konum Debug Ekranı'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatusCard(),
            const SizedBox(height: 16),
            _buildLocationCard(),
            const SizedBox(height: 16),
            _buildControlsCard(),
            const SizedBox(height: 16),
            _buildErrorCard(),
          ],
        ),
      ),
    );
  }

  /// Durum bilgisi kartı
  Widget _buildStatusCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Durum Bilgileri',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 8),
            _buildStatusRow('Takip Aktif', widget.locationProvider.isTracking),
            _buildStatusRow('İzin Durumu', _getPermissionStatusText() != 'N/A'),
            _buildStatusRow('Servis Durumu', _getServiceStatusText() != 'N/A'),
            _buildStatusRow('Yükleniyor', widget.locationProvider.isLoading),
          ],
        ),
      ),
    );
  }

  /// Konum bilgisi kartı
  Widget _buildLocationCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Konum Bilgileri',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 8),
            _buildLocationInfoRow(
              'Enlem',
              widget.locationProvider.currentLocation?.latitude.toString() ??
                  'N/A',
            ),
            _buildLocationInfoRow(
              'Boylam',
              widget.locationProvider.currentLocation?.longitude.toString() ??
                  'N/A',
            ),
            _buildLocationInfoRow(
              'Doğruluk',
              widget.locationProvider.currentLocation?.accuracy.toString() ??
                  'N/A',
            ),
            _buildLocationInfoRow(
              'Zaman Damgası',
              widget.locationProvider.currentLocation?.timestamp.toString() ??
                  'N/A',
            ),
            _buildLocationInfoRow('Konum Durumu', _getLocationStatusText()),
            Builder(
              builder: (context) {
                final location = widget.locationProvider.currentLocation;
                final displayName =
                    location?.fullAddress ??
                    (location?.locationName ?? 'Konum adı alınıyor...');
                return _buildLocationInfoRow('Konum Adı', displayName);
              },
            ),
            _buildLocationInfoRow('Kıble Yönü', _getQiblaDirectionText()),
          ],
        ),
      ),
    );
  }

  /// Kontrol kartı
  Widget _buildControlsCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Kontroller',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () {
                _showAddressDialog();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
              ),
              child: const Text('Adresten Konum'),
            ),
          ],
        ),
      ),
    );
  }

  /// Hata kartı
  Widget _buildErrorCard() {
    if (widget.locationProvider.errorMessage == null) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 4,
      color: Colors.red.shade100,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            const Icon(Icons.error, color: Colors.red, size: 24),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                widget.locationProvider.errorMessage!,
                style: const TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            IconButton(
              icon: const Icon(Icons.clear),
              color: Colors.red,
              onPressed: widget.locationProvider.clearError,
            ),
          ],
        ),
      ),
    );
  }

  /// Durum satırı
  Widget _buildStatusRow(String label, bool value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
          ),
          Text(
            value ? 'Evet' : 'Hayır',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: value ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  /// Konum bilgisi satırı
  Widget _buildLocationInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
          ),
          Flexible(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// İzin durum metni
  String _getPermissionStatusText() {
    switch (widget.locationProvider.permissionStatus) {
      case LocationPermissionStatus.granted:
        return 'Verildi';
      case LocationPermissionStatus.denied:
        return 'Reddedildi';
      case LocationPermissionStatus.permanentlyDenied:
        return 'Kalıcı Reddedildi';
      case LocationPermissionStatus.notDetermined:
        return 'Belirlenmedi';
      case LocationPermissionStatus.restricted:
        return 'Kısıtlı';
      default:
        return 'N/A';
    }
  }

  /// Servis durum metni
  String _getServiceStatusText() {
    switch (widget.locationProvider.serviceStatus) {
      case LocationServiceStatus.enabled:
        return 'Aktif';
      case LocationServiceStatus.disabled:
        return 'Devre Dışı';
      case LocationServiceStatus.unknown:
        return 'Bilinmiyor';
      default:
        return 'N/A';
    }
  }

  /// Konum durum metni
  String _getLocationStatusText() {
    if (widget.locationProvider.currentLocation == null) return 'N/A';

    switch (widget.locationProvider.currentLocation!.status) {
      case LocationStatus.gps:
        return 'GPS';
      case LocationStatus.network:
        return 'Ağ';
      case LocationStatus.passive:
        return 'Pasif';
      case LocationStatus.unavailable:
        return 'Ulaşılamaz';
      case LocationStatus.unknown:
        return 'Bilinmiyor';
    }
    // Bu satırı ekledim
  }

  /// Kıble yönü metni
  String _getQiblaDirectionText() {
    if (widget.locationProvider.currentLocation?.qiblaDirection == null) {
      return 'N/A';
    }
    return '${widget.locationProvider.currentLocation!.qiblaDirection!.toStringAsFixed(1)}°';
  }

  /// Adres dialog'u göster
  void _showAddressDialog() {
    final TextEditingController controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Adres Girin'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: 'Örn: İstanbul, Türkiye',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('İptal'),
          ),
          ElevatedButton(
            onPressed: () {
              final address = controller.text.trim();
              if (address.isNotEmpty) {
                widget.locationProvider.getLocationFromAddress(address);
                Navigator.pop(context);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('Bul'),
          ),
        ],
      ),
    );
  }
}
