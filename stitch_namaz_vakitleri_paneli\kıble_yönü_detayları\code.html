<!DOCTYPE html>
<html><head>
<meta charset="utf-8"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Spline+Sans%3Awght%40400%3B500%3B700" onload="this.rel='stylesheet'" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet"/>
<title>Stitch Design</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style type="text/tailwindcss">
        :root {
            --primary-color: #38e07b;
        }
    </style>
<style>
        body {
            min-height: max(884px, 100dvh);
        }
        .compass-bezel {
            background-image: radial-gradient(circle at center, #1c2620 50%, #111714 51%, #111714 60%, #1c2620 61%, #1c2620 100%);
            border: 4px solid #29382f;
        }
        .compass-inner-shadow {
            box-shadow: inset 0 0 20px rgba(0,0,0,0.5);
        }
    </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-[#111714]">
<div class="relative flex size-full min-h-screen flex-col bg-[#111714] dark justify-between group/design-root overflow-x-hidden" style='font-family: "Spline Sans", "Noto Sans", sans-serif;'>
<div class="flex-grow flex flex-col">
<div class="flex items-center bg-[#111714] p-4 pb-2 justify-between">
<button class="text-white flex size-10 shrink-0 items-center justify-center rounded-full hover:bg-white/10 transition-colors">
<span class="material-symbols-outlined">arrow_back</span>
</button>
<h2 class="text-white text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pr-10">Kıble Pusulası</h2>
</div>
<div class="flex-grow flex flex-col items-center justify-center text-white p-4 relative">
<div class="relative w-80 h-80 md:w-96 md:h-96 rounded-full flex items-center justify-center compass-bezel">
<div class="absolute w-full h-full rounded-full compass-inner-shadow"></div>
<div class="absolute w-full h-full">
<div class="absolute top-2 left-1/2 -translate-x-1/2 text-white font-bold text-xl">N</div>
<div class="absolute bottom-2 left-1/2 -translate-x-1/2 text-white/50 font-bold text-xl">S</div>
<div class="absolute left-2 top-1/2 -translate-y-1/2 text-white/50 font-bold text-xl">W</div>
<div class="absolute right-2 top-1/2 -translate-y-1/2 text-white/50 font-bold text-xl">E</div>
<div class="absolute h-full w-full" style="transform: rotate(30deg);"><div class="absolute top-3 left-1/2 -translate-x-1/2 text-white/50">|</div></div>
<div class="absolute h-full w-full" style="transform: rotate(60deg);"><div class="absolute top-3 left-1/2 -translate-x-1/2 text-white/50">|</div></div>
<div class="absolute h-full w-full" style="transform: rotate(120deg);"><div class="absolute top-3 left-1/2 -translate-x-1/2 text-white/50">|</div></div>
<div class="absolute h-full w-full" style="transform: rotate(150deg);"><div class="absolute top-3 left-1/2 -translate-x-1/2 text-white/50">|</div></div>
<div class="absolute h-full w-full" style="transform: rotate(210deg);"><div class="absolute top-3 left-1/2 -translate-x-1/2 text-white/50">|</div></div>
<div class="absolute h-full w-full" style="transform: rotate(240deg);"><div class="absolute top-3 left-1/2 -translate-x-1/2 text-white/50">|</div></div>
<div class="absolute h-full w-full" style="transform: rotate(300deg);"><div class="absolute top-3 left-1/2 -translate-x-1/2 text-white/50">|</div></div>
<div class="absolute h-full w-full" style="transform: rotate(330deg);"><div class="absolute top-3 left-1/2 -translate-x-1/2 text-white/50">|</div></div>
</div>
<div class="absolute inset-0 flex items-center justify-center transition-transform duration-500 ease-in-out" style="transform: rotate(138deg);">
<div class="relative w-full h-full">
<div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-full w-0 h-0" style="border-left: 12px solid transparent; border-right: 12px solid transparent; border-bottom: 80px solid var(--primary-color);"></div>
<div class="absolute top-1/2 left-1/2 -translate-x-1/2 w-4 h-4 bg-white/50 rounded-full" style="transform: translateY(-50%);"></div>
<div class="absolute top-1/2 left-1/2 -translate-x-1/2 w-1 h-1/2 bg-[var(--primary-color)]" style="transform-origin: center top;"></div>
</div>
</div>
<div class="absolute w-6 h-6 bg-[var(--primary-color)] rounded-full flex items-center justify-center" style="box-shadow: 0 0 10px var(--primary-color);">
<span class="material-symbols-outlined text-black text-lg" style="transform: rotate(-138deg);">navigation</span>
</div>
</div>
<div class="absolute bottom-8 text-center w-full px-4">
<div class="flex justify-between items-center bg-[#1c2620] p-4 rounded-xl">
<div>
<p class="text-white/60 text-sm">Konum</p>
<p class="text-white font-semibold">İstanbul, Türkiye</p>
</div>
<div>
<p class="text-white/60 text-sm">Kıble Açısı</p>
<p class="text-[var(--primary-color)] font-bold text-lg">138°</p>
</div>
</div>
<p class="text-white/60 mt-3 text-sm">Enlem: 41.0082° N, Boylam: 28.9784° E</p>
</div>
</div>
</div>
<div class="sticky bottom-0">
<div class="flex justify-around border-t border-[#29382f] bg-[#1c2620] px-4 pt-3 pb-5">
<a class="flex flex-col items-center justify-end gap-1 text-white/60 hover:text-white transition-colors" href="#">
<span class="material-symbols-outlined text-2xl">schedule</span>
<p class="text-xs font-medium tracking-[0.015em]">Vakitler</p>
</a>
<a class="flex flex-col items-center justify-end gap-1 text-white/60 hover:text-white transition-colors" href="#">
<span class="material-symbols-outlined text-2xl">checklist</span>
<p class="text-xs font-medium tracking-[0.015em]">Kaza</p>
</a>
<a class="flex flex-col items-center justify-end gap-1 text-[var(--primary-color)]" href="#">
<span class="material-symbols-outlined text-2xl">explore</span>
<p class="text-xs font-medium tracking-[0.015em]">Kıble</p>
</a>
<a class="flex flex-col items-center justify-end gap-1 text-white/60 hover:text-white transition-colors" href="#">
<span class="material-symbols-outlined text-2xl">settings</span>
<p class="text-xs font-medium tracking-[0.015em]">Ayarlar</p>
</a>
<a class="flex flex-col items-center justify-end gap-1 text-white/60 hover:text-white transition-colors" href="#">
<span class="material-symbols-outlined text-2xl">calculate</span>
<p class="text-xs font-medium tracking-[0.015em]">Kaza Hesapla</p>
</a>
</div>
</div>
</div>

</body></html>