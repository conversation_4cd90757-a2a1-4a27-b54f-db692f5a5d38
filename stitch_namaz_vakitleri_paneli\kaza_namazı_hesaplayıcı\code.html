<!DOCTYPE html>
<html><head>
<meta charset="utf-8"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Spline+Sans%3Awght%40400%3B500%3B700" onload="this.rel='stylesheet'" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet"/>
<title>Stitch Design</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-[#111714]">
<div class="relative flex size-full min-h-screen flex-col bg-[#111714] dark justify-between group/design-root" style='font-family: "Spline Sans", "Noto Sans", sans-serif;'>
<div class="flex-grow">
<header class="flex items-center p-4 pb-2 justify-between">
<button class="text-white">
<span class="material-symbols-outlined"> arrow_back_ios_new </span>
</button>
<h1 class="text-white text-xl font-bold leading-tight tracking-[-0.015em] flex-1 text-center">Kaza Hesapla</h1>
<div class="w-10"></div>
</header>
<main class="p-4 space-y-6">
<div class="space-y-4">
<div>
<label class="block text-white text-base font-medium leading-normal pb-2" for="birth-date">Doğum Tarihi</label>
<div class="relative">
<input class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-white focus:outline-0 focus:ring-2 focus:ring-[#38e07b] border-none bg-[#29382f] h-14 placeholder:text-[#9eb7a8] p-4 text-base font-normal leading-normal pr-12" id="birth-date" placeholder="GG/AA/YYYY" value=""/>
<span class="material-symbols-outlined absolute right-4 top-1/2 -translate-y-1/2 text-[#9eb7a8]"> calendar_today </span>
</div>
</div>
<div>
<label class="block text-white text-base font-medium leading-normal pb-2" for="prayer-start-date">Namaz Başlama Tarihi</label>
<div class="relative">
<input class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-white focus:outline-0 focus:ring-2 focus:ring-[#38e07b] border-none bg-[#29382f] h-14 placeholder:text-[#9eb7a8] p-4 text-base font-normal leading-normal pr-12" id="prayer-start-date" placeholder="GG/AA/YYYY" value=""/>
<span class="material-symbols-outlined absolute right-4 top-1/2 -translate-y-1/2 text-[#9eb7a8]"> calendar_today </span>
</div>
</div>
</div>
<p class="text-[#9eb7a8] text-sm font-normal leading-normal text-center">
            15 yaşından namaza başlama tarihinize kadar olan kaza namazı sayısı otomatik olarak hesaplanacaktır.
          </p>
<button class="flex w-full min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-14 px-4 bg-[#38e07b] text-[#111714] text-lg font-bold leading-normal tracking-[0.015em] shadow-lg shadow-[#38e07b]/20 hover:bg-opacity-90 transition-all">
<span class="truncate">Hesapla</span>
</button>
<div class="space-y-4 pt-6">
<h2 class="text-white text-lg font-bold leading-tight tracking-[-0.015em]">Kaza Namazı Durumu</h2>
<div class="bg-[#1c2620] rounded-xl p-4 space-y-2">
<div class="flex items-center justify-between">
<p class="text-white text-base font-medium">Sabah</p>
<div class="flex items-center gap-3 text-white">
<button class="text-xl font-medium flex h-8 w-8 items-center justify-center rounded-full bg-[#29382f] hover:bg-[#3d5044] transition-colors cursor-pointer">-</button>
<span class="text-lg font-bold w-6 text-center">0</span>
<button class="text-xl font-medium flex h-8 w-8 items-center justify-center rounded-full bg-[#29382f] hover:bg-[#3d5044] transition-colors cursor-pointer">+</button>
</div>
</div>
<hr class="border-t border-[#29382f]"/>
<div class="flex items-center justify-between">
<p class="text-white text-base font-medium">Öğle</p>
<div class="flex items-center gap-3 text-white">
<button class="text-xl font-medium flex h-8 w-8 items-center justify-center rounded-full bg-[#29382f] hover:bg-[#3d5044] transition-colors cursor-pointer">-</button>
<span class="text-lg font-bold w-6 text-center">0</span>
<button class="text-xl font-medium flex h-8 w-8 items-center justify-center rounded-full bg-[#29382f] hover:bg-[#3d5044] transition-colors cursor-pointer">+</button>
</div>
</div>
<hr class="border-t border-[#29382f]"/>
<div class="flex items-center justify-between">
<p class="text-white text-base font-medium">İkindi</p>
<div class="flex items-center gap-3 text-white">
<button class="text-xl font-medium flex h-8 w-8 items-center justify-center rounded-full bg-[#29382f] hover:bg-[#3d5044] transition-colors cursor-pointer">-</button>
<span class="text-lg font-bold w-6 text-center">0</span>
<button class="text-xl font-medium flex h-8 w-8 items-center justify-center rounded-full bg-[#29382f] hover:bg-[#3d5044] transition-colors cursor-pointer">+</button>
</div>
</div>
<hr class="border-t border-[#29382f]"/>
<div class="flex items-center justify-between">
<p class="text-white text-base font-medium">Akşam</p>
<div class="flex items-center gap-3 text-white">
<button class="text-xl font-medium flex h-8 w-8 items-center justify-center rounded-full bg-[#29382f] hover:bg-[#3d5044] transition-colors cursor-pointer">-</button>
<span class="text-lg font-bold w-6 text-center">0</span>
<button class="text-xl font-medium flex h-8 w-8 items-center justify-center rounded-full bg-[#29382f] hover:bg-[#3d5044] transition-colors cursor-pointer">+</button>
</div>
</div>
<hr class="border-t border-[#29382f]"/>
<div class="flex items-center justify-between">
<p class="text-white text-base font-medium">Yatsı</p>
<div class="flex items-center gap-3 text-white">
<button class="text-xl font-medium flex h-8 w-8 items-center justify-center rounded-full bg-[#29382f] hover:bg-[#3d5044] transition-colors cursor-pointer">-</button>
<span class="text-lg font-bold w-6 text-center">0</span>
<button class="text-xl font-medium flex h-8 w-8 items-center justify-center rounded-full bg-[#29382f] hover:bg-[#3d5044] transition-colors cursor-pointer">+</button>
</div>
</div>
</div>
</div>
</main>
</div>
<footer class="sticky bottom-0">
<div class="flex justify-around border-t border-[#29382f] bg-[#1c2620]/80 backdrop-blur-sm py-2">
<a class="flex flex-col items-center justify-end gap-1 text-[#9eb7a8] w-1/5" href="#">
<span class="material-symbols-outlined text-2xl"> schedule </span>
<p class="text-xs font-medium tracking-[0.015em]">Vakitler</p>
</a>
<a class="flex flex-col items-center justify-end gap-1 text-[#9eb7a8] w-1/5" href="#">
<span class="material-symbols-outlined text-2xl"> event_repeat </span>
<p class="text-xs font-medium tracking-[0.015em]">Kaza</p>
</a>
<a class="flex flex-col items-center justify-end gap-1 text-[#9eb7a8] w-1/5" href="#">
<span class="material-symbols-outlined text-2xl"> explore </span>
<p class="text-xs font-medium tracking-[0.015em]">Kıble</p>
</a>
<a class="flex flex-col items-center justify-end gap-1 text-[#9eb7a8] w-1/5" href="#">
<span class="material-symbols-outlined text-2xl"> settings </span>
<p class="text-xs font-medium tracking-[0.015em]">Ayarlar</p>
</a>
<a class="flex flex-col items-center justify-end gap-1 text-[#38e07b] w-1/5" href="#">
<span class="material-symbols-outlined text-2xl" style="font-variation-settings: 'FILL' 1;"> calculate </span>
<p class="text-xs font-bold tracking-[0.015em]">Kaza Hesapla</p>
</a>
</div>
</footer>
</div>

</body></html>