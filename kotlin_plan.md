Konseptten Konsola: <PERSON><PERSON><PERSON> ile Yapay Zeka Destekli Bir Namaz Vakti Android Uygulaması Oluşturmak İçin Kapsamlı Yol HaritasıGiriş: Araçlarınızı Seçmek - Modern Bir Android Teknolojisi Yığını TercihiBir mobil uygulama geliştirme yolculuğuna çıkarken, özellikle programlama deneyimi olmayan ve yapay zeka destekli araçlardan yararlanmayı planlayan bir geliştirici için en kritik karar, projenin üzerine inşa edileceği teknolojik temeldir. Bu rapor, bir namaz vakti takibi uygulaması için en uygun, modern ve geleceğe dönük teknoloji yığınını belirlemekte ve bu temeller üzerine başarılı bir ürün inşa etmek için adım adım bir yol haritası sunmaktadır.Neden Projeniz İçin Tartışmasız Tercih Kotlin Olmalı?Android uygulama geliştirme dü<PERSON>tl<PERSON>, artık Java'ya bir alternatif değil, Google tarafından resmi olarak desteklenen ve yeni projeler için varsayılan standart haline gelmiş bir dildir.1 Bu projenin temel dili olarak Kotlin'in seçilmesi, bir dizi stratejik avantaj sunmaktadır:Özlülük ve Azaltılmış Standart Kod: Kotlin, aynı işlevselliği başarmak için Java'ya kıyasla önemli ölçüde daha az kod gerektirir.5 Bu durum, özellikle yapay zeka destekli bir iş akışı için hayati bir avantajdır. Daha az kod, yapay zekaya verilecek komutların daha basit olmasını, üretilen kodun bir insan tarafından daha kolay doğrulanmasını ve projenin genel bakımının daha kolay olmasını sağlar.Boş Değer (Null) Güvenliği: Kotlin'in tip sistemi, Java'da sıkça karşılaşılan ve can sıkıcı bir hata olan NullPointerException çökmelerini ortadan kaldırmak üzere tasarlanmıştır.5 Bu yerleşik güvenlik ağı, özellikle programlamaya yeni başlayan bir geliştirici için paha biçilmezdir.Modern Özellikler: Eşzamansız görevler için coroutines, veri sınıfları (data classes) ve genişletme fonksiyonları (extension functions) gibi modern dil özellikleri, karmaşık işlemleri basitleştirir ve geliştirme sürecini hızlandırır.5Tam Java Uyumluluğu: Bu proje tamamen Kotlin ile inşa edilecek olsa da, Kotlin'in mevcut geniş Java kütüphaneleri ekosistemini sorunsuz bir şekilde kullanabilmesi önemli bir güvencedir.4Jetpack Compose ile Tanışma: Android Arayüzünün GeleceğiGeleneksel XML tabanlı arayüz tasarım sisteminin yerini alan Jetpack Compose, modern, bildirimsel (declarative) ve yerel Android kullanıcı arayüzleri oluşturma aracıdır.2 Compose, "öncelikli olarak Kotlin" için tasarlanmıştır, bu da birçok yeni Android özelliğinin ve kütüphanesinin yalnızca bu teknolojiyle uyumlu olacağı anlamına gelir.3Bildirimsel arayüzün temel konsepti, belirli bir veri durumunda arayüzün nasıl görünmesi gerektiğini tanımlamaktır. Geliştirici, arayüz elemanlarını manuel olarak değiştirmek yerine, sadece durumu günceller ve Compose, arayüzü bu yeni duruma göre otomatik olarak yeniden çizer.11 Bu yaklaşım, yapay zekaya "öğelerin bir listesini göster" gibi daha doğal ve betimleyici komutlar vermeyi mümkün kılar, bu da geliştirme sürecini daha sezgisel ve daha az hataya açık hale getirir.Mimari Plan: ViewModel, StateFlow ve DataStoreUygulamanın sağlam ve sürdürülebilir olması için kullanılacak temel mimari bileşenler şunlardır:ViewModel: Ekran döndürme gibi yapılandırma değişiklikleri sırasında veri kaybını önleyen bir veri kasası olarak düşünülebilir. Verileri önbelleğe alarak kullanıcı arayüzünün bu verileri tekrar tekrar çekmesini engeller.12StateFlow: Kotlin Coroutines'den gelen modern bir veri akışıdır. Kullanıcı arayüzü, veri güncellemeleri için bu akışı "dinler" ve böylece ekranın her zaman en son veriyi yansıtmasını sağlar.15Jetpack DataStore: Kullanıcının seçtiği şehir veya hesaplama yöntemi gibi basit tercihleri saklamak için kullanılan, eski SharedPreferences'ın yerini alan modern, güvenli ve eşzamansız bir çözümdür.17Yapay Zeka ile Geliştirme: Bu Yol Haritası Neden YZ Destekli Geliştirmeye Uygun?Seçilen bu teknoloji yığını, yapay zeka kod üreticileriyle çalışmak için benzersiz bir şekilde uygundur.20 Kotlin'in özlülüğü ve Jetpack Compose'un bildirimsel yapısı, doğal dil komutlarının daha etkili bir şekilde koda dönüştürülmesini sağlar. Bu yol haritası, her biri net bir hedef, ilgili kavramların açıklaması ve yapay zekaya bağlam sağlamak için kullanılabilecek örnek kod parçacıkları içeren küçük, mantıksal ve "komut verilebilir" adımlara bölünmüştür. Bu yaklaşım, teknik olmayan bir kurucunun yapay zeka kullanarak başarılı bir proje geliştirme olasılığını önemli ölçüde artırır.Tablo 1: Önerilen Teknoloji Yığını ÖzetiBileşenUygulamadaki RolüSeçim NedeniKotlinProgramlama DiliÖzlü, güvenli, Google'ın resmi dili.4Jetpack ComposeArayüz (UI) KitiModern, bildirimsel, öncelikli olarak Kotlin için.2ViewModelArayüz Durum TutucusuYapılandırma değişikliklerinden etkilenmez, mantığı ayırır.12StateFlowReaktif Veri AkışıArayüzü veri değişikliklerinden verimli bir şekilde haberdar eder.15DataStoreKullanıcı Tercihleri DepolamasıEşzamansız, SharedPreferences'ın güvenli alternatifi.17RetrofitAğ/API İstemcisiAPI iletişimi için endüstri standardı.Bölüm 1: Temelleri Atmak - Ortam ve Proje KurulumuHer inşaat projesinde olduğu gibi, yazılım geliştirmede de sağlam bir temel atmak, projenin gelecekteki başarısı için elzemdir. Bu bölüm, geliştirme ortamının kurulmasından projenin ilk iskeletinin oluşturulmasına kadar olan adımları detaylandırmaktadır.Android Studio'nun Kurulumu ve YapılandırılmasıAndroid Studio, Android uygulamaları geliştirmek için resmi Entegre Geliştirme Ortamı'dır (IDE). Kurulum süreci oldukça basittir:İndirme: Resmi Android Geliştirici web sitesinden Android Studio'nun en son kararlı sürümünü indirin.21Kurulum: İndirilen kurulum dosyasını çalıştırın ve kurulum sihirbazındaki adımları takip edin. Bu işlem, IDE'nin kendisiyle birlikte Android SDK (Yazılım Geliştirme Kiti) ve test için kullanılacak Emülatör gibi temel bileşenleri de kuracaktır.21Sistem Gereksinimleri: Kuruluma başlamadan önce, bilgisayarınızın minimum sistem gereksinimlerini (örneğin, 8 GB RAM, en az 8 GB boş disk alanı) karşıladığından emin olun.21Android Proje Yapısını AnlamakYeni bir Android projesi oluşturulduğunda, çeşitli klasör ve dosyalar içeren bir yapı ortaya çıkar. Başlangıç için en önemli olanlar şunlardır:app/src/main/java/[paket_adınız]: Tüm Kotlin kod dosyalarının bulunacağı yer burasıdır.app/src/main/res: Uygulamanın resimler, ikonlar, arayüz düzenleri (XML tabanlı projeler için) ve diğer kaynak dosyalarını içerir.build.gradle.kts (Modül: app): Uygulamanın "alışveriş listesi" olarak düşünülebilir. Projeye eklenecek harici kütüphaneler (bağımlılıklar) burada tanımlanır.İlk Jetpack Compose Projenizi OluşturmaAndroid Studio'yu başlattıktan sonra, "New Project" sihirbazını kullanarak projenizi oluşturun:Şablon Seçimi: "Phone and Tablet" sekmesi altında, Empty Activity şablonunu seçin. Bu şablon, Jetpack Compose için temel kurulumu içerir.10Proje Yapılandırması: Projenize bir isim (Name), paket adı (Package name) ve kaydedileceği konumu (Save location) belirtin. Dil (Language) açılır menüsünde Kotlin'in tek seçenek olduğunu göreceksiniz, çünkü Jetpack Compose yalnızca Kotlin ile çalışır.10Minimum API Seviyesi: Minimum API seviyesini, Compose'un gerektirdiği gibi API 21 veya daha yüksek bir seviyeye ayarlayın.10Bu adımlar tamamlandığında, Android Studio temel bir "Merhaba Dünya" uygulaması içeren bir proje oluşturacaktır.Temel Bağımlılıkları (Kütüphaneleri) YapılandırmaBağımlılıklar, uygulamanıza ağ iletişimi veya veri depolama gibi belirli işlevler ekleyen önceden oluşturulmuş kod paketleridir. Bu projenin temel işlevleri için birkaç temel kütüphaneye ihtiyaç duyulacaktır. Bu kütüphaneler, build.gradle.kts (Module :app) dosyasındaki dependencies bloğuna eklenir. Yapay zekaya şu şekilde komut verilebilir: "Ağ işlemleri için en son kararlı Retrofit, kullanıcı tercihleri için Jetpack DataStore ve Compose için ViewModel yaşam döngüsü bağımlılıklarını projeye ekle."Projenin başlangıcında, temiz ve ölçeklenebilir bir temel oluşturmak kritik öneme sahiptir. Yeni başlayan bir geliştiricinin (ve yönlendirdiği yapay zekanın) tüm kodu tek bir MainActivity.kt dosyasına yığması yaygın bir hatadır. Bu durum, proje büyüdükçe yönetilemez hale gelir. Bu sorunu en başından önlemek için, herhangi bir özellik kodu yazmadan önce projenin içinde mantıksal bir paket yapısı oluşturulmalıdır. Örneğin, ui (kullanıcı arayüzü bileşenleri için), data (veri kaynakları ve modeller için) ve viewmodel (ViewModel sınıfları için) gibi klasörler oluşturmak, projeyi düzenli tutar. Bu yapı, geliştirici için zihinsel bir harita sağlar ve yapay zekaya "data paketi içinde PrayerTimesRepository adında yeni bir sınıf oluştur" gibi daha spesifik ve etkili komutlar vermeyi mümkün kılar. Bu proaktif organizasyon, büyük bir mimari kusurun ortaya çıkmasını en başından engeller.Bölüm 2: Jetpack Compose ile Kullanıcı Arayüzünü OluşturmaJetpack Compose, Android'de kullanıcı arayüzü (UI) oluşturma şeklini temelden değiştirir. Bu bölümde, uygulamanın ana ekranlarını ve ayarlar menüsünü tasarlamak için Compose'un temel yapı taşları ele alınacaktır.Composable Fonksiyonların TemelleriCompose'daki her arayüz elemanı bir @Composable fonksiyonudur. Bunlar, ekranda neyin gösterileceğini tanımlayan standart Kotlin fonksiyonlarıdır.11Temel Düzen Bileşenleri:Column: Öğeleri dikey olarak sıralar.Row: Öğeleri yatay olarak sıralar.Box: Öğeleri üst üste yığar (Z ekseninde).24Temel Arayüz Elemanları: Text (metin), Button (düğme), Icon (simge) ve Image (resim) gibi bileşenler, arayüzün temel yapı taşlarıdır.23Modifier'lar: Bir composable'ın görünümünü ve davranışını değiştirmek için kullanılır. Örneğin, Modifier.padding(16.dp) bir elemanın etrafına boşluk ekler veya Modifier.fillMaxWidth() elemanın genişliğini ebeveyninin tamamını kaplayacak şekilde ayarlar.24Ana Ekranı Tasarlama: Namaz Vakitlerini GörüntülemeUygulamanın ana ekranı, günlük namaz vakitlerini net bir şekilde sunmalıdır. Bu ekranı oluşturmak için adımlar şunlardır:PrayerTimesScreen Composable'ı Oluşturma: Bu, ana ekranın tamamını kapsayacak olan ana composable fonksiyondur.LazyColumn Kullanımı: Kaydırılabilir bir liste göstermek için en verimli yol LazyColumn kullanmaktır. Bu bileşen, yalnızca ekranda görünür olan öğeleri oluşturarak performansı artırır.26PrayerTimeRow Composable'ı Oluşturma: Her bir namaz vaktini (örneğin, "İmsak" ve "04:15") temsil eden yeniden kullanılabilir bir satır bileşeni oluşturulur. Bu, kod tekrarını önler ve arayüzü modüler hale getirir.Yer Tutucu Veri Kullanımı: Başlangıçta, gerçek API verileri bağlanmadan önce arayüzü oluşturmak ve test etmek için sahte (placeholder) veriler kullanılır.Ayarlar Ekranını Oluşturma: Geçiş Düğmeleri ve SeçeneklerAyarlar ekranı, kullanıcının uygulamayı kişiselleştirmesine olanak tanır.SettingsScreen Composable'ı Oluşturma: Ayarlar menüsünü içerecek ana bileşendir.Switch Bileşeni: "Bildirimleri Etkinleştir" gibi seçenekleri açıp kapatmak için Switch bileşeni kullanılır.27Tıklanabilir Satırlar: "Şehir Seç" veya "Hesaplama Yöntemi" gibi başka bir ekrana yönlendiren veya bir diyalog penceresi açan seçenekler için tıklanabilir Row bileşenleri oluşturulur.29Profesyonel bir Compose mimarisinin temel taşı, "state hoisting" (durum yukarı taşıma) adı verilen bir tekniktir. Basit bir Switch bileşeni, kendi checked durumunu remember { mutableStateOf(true) } kullanarak kendi içinde yönetebilir.27 Ancak bu, bileşeni "durum bilgisine sahip" (stateful) yapar ve dışarıdan kontrol edilmesini zorlaştırır. Eğer bu anahtarın durumu başka bir arayüz elemanını etkileyecekse veya kaydedilmesi gerekiyorsa, bu yaklaşım yetersiz kalır.Doğru yaklaşım, durumu yukarı taşımaktır.15 Durum, Switch'i çağıran üst bileşende (veya ViewModel'de) tutulur ve Switch'e bir parametre olarak geçirilir. Switch ayrıca, tıklandığında çağrılacak bir onCheckedChange fonksiyonu alır. Bu şekilde, Switch bileşeni "durum bilgisi olmayan" (stateless) hale gelir: yalnızca kendisine verilen durumu gösterir ve olayları yukarıya bildirir. Bu, bileşeni yeniden kullanılabilir, test edilebilir ve Bölüm 4'te ele alınacak olan Tek Yönlü Veri Akışı (UDF) mimarisine mükemmel uyumlu hale getirir. Bu nedenle, bu yol haritası, durum yukarı taşımayı ileri bir konu olarak değil, bileşen oluşturmanın varsayılan ve doğru yolu olarak öğretir.Material Design 3 ile Bütünsel Bir Görünüm ve His UygulamaMaterial Design, Google'ın tasarım sistemidir ve Android uygulamalarına tutarlı ve modern bir görünüm kazandırır. Jetpack Compose, Material Design 3 ile tam entegre gelir.MaterialTheme Kullanımı: Uygulamanın ui/theme paketinde bulunan MaterialTheme, renkler, tipografi ve şekiller gibi stil özelliklerini merkezi olarak yönetir. Tüm composable'lar bu tema içine sarılarak tutarlı bir görünüm elde edilir.9Temayı Özelleştirme: Color.kt, Type.kt ve Shape.kt dosyaları düzenlenerek uygulamanın birincil rengi gibi tema özellikleri kolayca özelleştirilebilir.Bölüm 3: Namaz Vakti Veri Kaynağını Entegre EtmeUygulamanın kalbi, namaz vakitlerini sağlayan harici veri kaynağıdır. Bu bölümde, bu verileri almak ve uygulamada kullanılabilir hale getirmek için gerekli adımlar açıklanmaktadır.REST API'leri AnlamakBir REST API (Uygulama Programlama Arayüzü), bir uygulamanın internet üzerinden bir sunucudaki verilere erişmesini sağlayan bir aracıdır. Bunu bir restorandaki garsona benzetebiliriz: Uygulama (müşteri) bir istekte bulunur (sipariş verir), API (garson) bu isteği sunucuya (mutfak) iletir, veriyi (yemeği) alır ve uygulamaya geri getirir. Bu proje için Aladhan API adlı ücretsiz ve halka açık servis kullanılacaktır.32Aladhan API'sine Retrofit ile BağlanmaRetrofit, Android'de ağ istekleri yapmak için endüstri standardı haline gelmiş bir kütüphanedir. Kurulumu üç ana adımdan oluşur:API Arayüzünü Tanımlama: Aladhan API'sinin getTimingsByCity gibi uç noktalarına karşılık gelen fonksiyonları içeren bir Kotlin interface'i oluşturulur.Retrofit Nesnesini Oluşturma: API'nin temel URL'sini ve verileri nasıl dönüştüreceğini belirten bir Retrofit nesnesi yapılandırılır.JSON Dönüştürücü Ekleme: API'den gelen veriler genellikle JSON formatındadır. Retrofit'in bu metin tabanlı veriyi Kotlin nesnelerine dönüştürebilmesi için Gson veya Moshi gibi bir dönüştürücü kütüphanesi eklenir.API Yanıtını (JSON) Kullanılabilir Kotlin Nesnelerine AyrıştırmaJSON (JavaScript Object Notation), API'ler için standart bir veri formatıdır. Aladhan API'sinden gelen yanıt, namaz vakitlerini ve diğer meta verileri içeren bir JSON yapısıdır.35 Bu yapıyı uygulamada kolayca kullanabilmek için, JSON'daki alanlarla birebir eşleşen Kotlin data class'ları oluşturulur. Bu sayede Retrofit, gelen JSON yanıtını otomatik olarak bu veri sınıflarının nesnelerine dönüştürebilir.Aladhan API'si, tek bir günün vakitlerini (/v1/timingsByCity) ve bütün bir ayın takvimini (/v1/calendarByCity) almak için ayrı uç noktalar sunar.37 Acemi bir uygulama, her açıldığında günlük uç noktayı çağırabilir. Ancak bu, verimsizdir ve sürekli internet bağlantısı gerektirir. Çok daha sağlam ve verimli bir yaklaşım, aylık takvim uç noktasını bir kez çağırmaktır. Uygulama, bu 30 günlük namaz vakti verisini yerel bir veritabanına veya basit bir dosyaya kaydedebilir. Sonraki açılışlarda, uygulama önce mevcut gün için geçerli yerel verinin olup olmadığını kontrol eder. Eğer varsa, veriyi anında ve ağ çağrısı yapmadan gösterir. Eğer veri yoksa (veya yeni bir ay başlamışsa), o zaman yeni ayın verilerini çeker ve yerel olarak kaydeder. Bu "önce önbelleğe bak" stratejisi, performansı önemli ölçüde artırır, veri tüketimini azaltır ve uygulamaya çevrimdışı çalışma yeteneği kazandırır.Tablo 2: Aladhan API Uç Nokta ReferansıUç NoktaAçıklamaGerekli Parametrelerİsteğe Bağlı ParametrelerÖrnek URL/v1/timingsByCityŞehir/ülkeye göre tek bir günün namaz vakitlerini alır.city (String), country (String)method (Int), school (Int).../timingsByCity?city=Istanbul&country=Turkey&method=13/v1/calendarByCityMiladi bir ayın tam takvimini alır.year (Int), month (Int), city, countrymethod (Int), school (Int).../calendarByCity?year=2025&month=10&city=.../v1/hijriCalendarByCityHicri bir ayın tam takvimini alır.year (Int), month (Int), city, countrymethod (Int), school (Int).../hijriCalendarByCity?year=1447&month=3&city=...Bölüm 4: İstikrar için Mimari - Durum Yönetimi ve Hata Kontrolüİyi bir mimari, bir uygulamanın uzun vadede sürdürülebilir, test edilebilir ve hatasız olmasını sağlar. Bu bölümde, uygulamanın veri akışını ve durum yönetimini organize etmek için modern Android mimari prensipleri ele alınacaktır.ViewModel'in Rolü: Mantığı Arayüzden AyırmakAndroid Jetpack'in ViewModel sınıfı, ekranın "beyni" olarak işlev görür.12 Temel görevi, kullanıcı arayüzü (UI) ile ilgili verileri tutmak ve yönetmektir. ViewModel, ekran döndürme gibi yapılandırma değişiklikleri sırasında yok olmaz; bu sayede içindeki veriler korunur ve yeniden oluşturulan arayüze anında sunulur.13 Bu, veri çekme işlemlerinin tekrarlanmasını önler ve kullanıcı deneyimini akıcı hale getirir.Kotlin StateFlow ile Arayüz Durumunu YönetmeViewModel'den arayüze veri akışını sağlamanın en modern ve tavsiye edilen yolu, Kotlin Coroutines'in bir parçası olan StateFlow kullanmaktır.15Arayüz Durum Sınıfı (UiState) Tanımlama: Ekranda gösterilmesi gereken her şeyi temsil eden bir veri sınıfı oluşturulur. Bu sınıf, bir yükleme göstergesi, namaz vakitleri listesi veya bir hata mesajı gibi farklı durumları içerebilir.StateFlow'u Arayüze Sunma: ViewModel, StateFlow<PrayerTimesUiState> türünde bir nesneyi arayüze sunar.Durumu Gözlemleme: Composable fonksiyon, collectAsStateWithLifecycle() kullanarak bu StateFlow'u dinler. Bu, reaktif bir bağlantı kurar: ViewModel durumu her güncellediğinde, arayüz bu değişikliği yansıtmak için otomatik olarak kendini yeniden çizer.15Arayüz durumunu modellemek için basit bir veri sınıfı yerine sealed interface kullanmak, hem yapay zeka dostu hem de yeni başlayanlar için sağlam bir yöntemdir. isLoading, isError gibi boole ve nullable alanlar içeren tek bir veri sınıfı, isLoading true iken error mesajının da dolu olması gibi geçersiz durumlara izin verebilir. Bunun yerine, sealed interface kullanımı bu sorunu kökten çözer 41:Kotlinsealed interface PrayerTimesUiState {
data object Loading : PrayerTimesUiState
data class Success(val prayerTimes: List<...>) : PrayerTimesUiState
data class Error(val message: String) : PrayerTimesUiState
}
Bu yapı, arayüz kodunda (veya yapay zekaya verilen komutta) when ifadesi kullanıldığında, derleyicinin tüm olası durumları (Loading, Success, Error) ele almayı zorunlu kılmasını sağlar. Bu, bir durumu unutma veya geçersiz bir arayüz durumu oluşturma olasılığını ortadan kaldırarak kodun güvenilirliğini artırır.Tek Yönlü Veri Akışı (UDF) Modelini UygulamaUygulamanın mimarisi, Tek Yönlü Veri Akışı (Unidirectional Data Flow - UDF) prensibine dayanacaktır.31 Bu modelde veri, tahmin edilebilir tek bir yönde akar:Olay (Event): Arayüz, bir kullanıcı etkileşimini (örneğin, "Yenile" düğmesine tıklama) bir olay olarak ViewModel'e gönderir.Durumu Güncelle (Update State): ViewModel, bu olayı işler (örneğin, yeni veri çekmek için ilgili fonksiyonu çağırır) ve StateFlow'u yeni bir durumla (örneğin, PrayerTimesUiState.Loading) günceller.Durumu Göster (Display State): StateFlow'u dinleyen arayüz, yeni durumu alır ve kendini buna göre yeniden çizer.İnternet Yok Senaryolarını Ele Alma ve Hata Mesajları GöstermeAğ bağlantısı hataları kaçınılmazdır. Bu durumları zarif bir şekilde yönetmek, iyi bir kullanıcı deneyimi için kritiktir.Hata Durumunu Modelleme: Yukarıda tanımlanan PrayerTimesUiState.Error durumu, bu senaryo için kullanılır.Hata Yakalama: ViewModel içinde ağ istekleri yapan kod blokları try-catch ile sarmalanır. Bir IOException yakalandığında (bu genellikle bir ağ sorununa işaret eder), ViewModel durumu bir hata mesajı içeren Error durumuna günceller.42Hata Mesajını Gösterme: Arayüz, when ifadesi içinde Error durumunu kontrol eder ve eğer durum bu ise, namaz vakitleri listesi yerine kullanıcıya bir hata mesajı gösterir.45Bölüm 5: Kişiselleştirme - Konum ve Kullanıcı TercihleriUygulamanın kullanışlılığı, kullanıcının konumuna ve kişisel tercihlerine göre doğru verileri sunma yeteneğine bağlıdır.Kullanıcıdan Konum İzinlerini İstemeAndroid, kullanıcı gizliliğini korumak için uygulamaların hassas verilere erişmeden önce izin istemesini zorunlu kılar.Manifest Dosyasını Güncelleme: AndroidManifest.xml dosyasına ACCESS_COARSE_LOCATION (yaklaşık konum) ve ACCESS_FINE_LOCATION (hassas konum) izinleri eklenir.47İzin İsteme: İzin, uygulamanın herhangi bir anında değil, kullanıcı konum gerektiren bir özelliğe dokunduğunda (örneğin, "Konumumu Kullan" düğmesi) istenmelidir. Bu, "bağlam içinde izin isteme" en iyi uygulamasıdır. Jetpack Compose'da bu işlem, modern Activity Result API'leri kullanılarak gerçekleştirilir.48 En iyi pratik, her iki izni (hassas ve yaklaşık) aynı anda istemektir.48Kullanıcının Mevcut Koordinatlarını Almaİzin alındıktan sonra, cihazın konumu FusedLocationProviderClient API'si kullanılarak alınır. Bu, Google'ın önerdiği konum alma yöntemidir.50 Kod, cihazın bilinen son konumunu alacak ve bu konumun null olabileceği durumları (örneğin, konum servisleri kapalıysa) ele alacak şekilde yazılmalıdır.50Jetpack DataStore ile Kullanıcı Ayarlarını KaydetmeKullanıcının şehir seçimi, tercih ettiği hesaplama yöntemi gibi ayarları kaydetmek için Jetpack DataStore kullanılır. DataStore, eski SharedPreferences'a göre üstündür çünkü eşzamansızdır (UI iş parçacığını engellemez), hata sinyali verir ve Kotlin Coroutines/Flow ile doğal bir şekilde entegre olur.17Bu proje için anahtar-değer tabanlı Preferences DataStore kullanılacaktır:DataStore Oluşturma: preferencesDataStore delegesi kullanılarak bir DataStore nesnesi oluşturulur.19Anahtarları Tanımlama: Her ayar için türü belli anahtarlar oluşturulur (örneğin, stringPreferencesKey("city"), intPreferencesKey("calculation_method")).51Veri Yazma ve Okuma: Veri yazmak için dataStore.edit {... } fonksiyonu, veri okumak için ise dataStore.data.map {... } ile bir Flow oluşturulur.51Kaydedilen Tercihleri API İsteklerine UygulamaViewModel, DataStore'dan kullanıcının kaydettiği şehri, ülkeyi ve hesaplama yöntemini okur. Bu bilgiler daha sonra namaz vakitlerini getiren API çağrısına parametre olarak eklenir.Namaz vakti hesaplama yönteminin seçimi, bölgesel ve kişisel bir tercihtir. Aladhan API, method parametresi aracılığıyla (MWL, ISNA, Diyanet gibi) ve school parametresi aracılığıyla (Şafii veya Hanefi fıkıhlarına göre ikindi vakti hesaplaması) çok sayıda yöntemi destekler.53 Bu teknik isimler ortalama bir kullanıcı için anlamsızdır. Bu nedenle, ayarlar ekranı bu seçenekleri "Müslüman Dünya Ligi (Avrupa ve Uzak Doğu'da yaygın)" veya "ISNA (Kuzey Amerika'da yaygın)" gibi anlaşılır açıklamalarla sunmalıdır. Bu, uygulamayı basit bir araç olmaktan çıkarıp, kültürel olarak duyarlı ve kullanıcı odaklı bir deneyime dönüştürür.Tablo 3: Namaz Vakti Hesaplama Yöntemleri ve ParametreleriYöntem AdıAPI method DeğeriTipik Bölge/Kullanım Alanıİkindi Hesabı (API school Değeri)Müslüman Dünya Ligi (MWL)2Avrupa, Uzak Doğu, ABD'nin bazı bölgeleriStandart (Şafii): school=0Kuzey Amerika İslam Topluluğu (ISNA)4ABD, KanadaStandart (Şafii): school=0Mısır Genel Araştırma Kurumu5Afrika, Suriye, LübnanStandart (Şafii): school=0Mekke Ümmü'l-Kura Üniversitesi3Suudi Arabistan, Körfez ÜlkeleriStandart (Şafii): school=0Karaçi İslami İlimler Üniversitesi1Pakistan, Hindistan, BangladeşHanefi: school=1Diyanet İşleri Başkanlığı13TürkiyeHanefi: school=1Şii İsnâaşeriyye0Şii topluluklarStandart (Şafii): school=0Bölüm 6: Uyarı Sistemi - Namaz Vakti Bildirimlerini UygulamaBir namaz vakti uygulamasının en temel özelliklerinden biri, kullanıcıyı vakitler girdiğinde haberdar etmektir. Bu, arka planda güvenilir bir şekilde çalışması gereken bir sistem gerektirir.Modern Android'de Bildirim İzinlerini İstemeAndroid 13 ve üzeri sürümlerde, uygulamaların bildirim göndermeden önce kullanıcıdan açıkça izin alması gerekmektedir.56Manifest'e İzin Ekleme: AndroidManifest.xml dosyasına <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/> izni eklenir.56İzin İsteme: Bu izin, kullanıcı bildirimleri ilk kez etkinleştirdiğinde (örneğin, ayarlar ekranındaki bir anahtarla) istenmelidir. Bu, konum izniyle aynı Activity Result API deseni kullanılarak yapılır.59AlarmManager ile Hassas Alarmlar ZamanlamaAlarmManager, uygulama kapalıyken bile belirli bir zamanda bir işlem başlatmak için kullanılan sistem servisidir.62 Namaz vakitleri gibi zaman açısından kritik uyarılar için bu servis vazgeçilmezdir.Modern Android sürümleri, pil ömrünü korumak için hassas alarmların kullanımını kısıtlar ve SCHEDULE_EXACT_ALARM özel iznini gerektirir.64 Ancak, bu uygulama bir takvim/alarm uygulaması kategorisine girdiği için, bunun yerine USE_EXACT_ALARM iznini manifest dosyasında beyan edebilir. Bu, "normal" bir izin olduğu için kurulum sırasında otomatik olarak verilir ve kullanıcıdan ek bir izin isteme sürecini ortadan kaldırır. Bu, bu tür bir uygulama için kritik ve iş akışını basitleştiren bir ayrıntıdır.65Alarmlar, kullanıcı tarafından görülebilir ve zaman açısından kritik oldukları için setAlarmClock() metodu kullanılarak ayarlanmalıdır. Bu metod, cihaz uyku modundayken bile alarmın tam zamanında tetiklenmesini sağlar.62Her Namaz Vakti için Bildirim Oluşturma ve GörüntülemeBir alarm tetiklendiğinde, sistem bir BroadcastReceiver'ı çalıştırır. Bu alıcı içinde, NotificationManagerCompat kullanılarak bir bildirim oluşturulur ve kullanıcıya gösterilir. Bildirim; bir başlık ("Akşam Vakti"), metin ("Namaz vakti girdi"), bir simge ve bir ses içermelidir.Cihaz Yeniden Başlatıldıktan Sonra Alarmların KorunmasıKullanıcı telefonunu yeniden başlattığında, ayarlanmış olan tüm alarmlar silinir. Bunları yeniden kurmak, uygulamanın güvenilirliği için hayati önem taşır. Bu, ACTION_BOOT_COMPLETED sistem olayını dinleyen başka bir BroadcastReceiver ile yapılır. Cihaz açıldığında, bu alıcı çalışır ve o günün (veya sonraki günlerin) alarmlarını yeniden programlar.62Bölüm 3'te bahsedilen tam aylık veriyi yerel olarak saklama stratejisi burada da büyük bir avantaj sağlar. Uygulama, yalnızca o günün 5 alarmını kurmak yerine, önümüzdeki birkaç günün (örneğin bir hafta) tüm alarmlarını önceden programlayabilir. Bu, uygulamayı daha dayanıklı hale getirir. Kullanıcı uygulamayı birkaç gün açmasa veya cihazı geçici olarak internete bağlı olmasa bile, AlarmManager kuyruğunda bekleyen alarmlar sayesinde bildirimler zamanında gelmeye devam edecektir. Bu proaktif zamanlama stratejisi, "ayarla ve unut" tarzı bir kullanıcı deneyimi yaratarak uygulamanın temel değerini artırır.Bölüm 7: Gelişmiş Özellik - Kıble Pusulası OluşturmaKıble yönünü gösteren bir pusula, bu tür bir uygulama için değerli bir eklentidir. Bu özellik, cihazın sensörlerinden ve coğrafi konum hesaplamalarından yararlanır.Cihaz Sensörlerine Erişme: İvmeölçer ve ManyetometreAndroid'in sensör çerçevesi, SensorManager aracılığıyla cihazın donanım sensörlerine erişim sağlar.67 Cihazın gerçek dünyadaki yönünü belirlemek için iki sensörün verileri birleştirilmelidir:İvmeölçer (Accelerometer): Cihazın yerçekimine göre eğimini belirler.Manyetometre (Magnetometer): Dünyanın manyetik alanına göre yönünü belirler.69Bu iki sensörden gelen ham veriler, SensorManager.getRotationMatrix() ve SensorManager.getOrientation() fonksiyonları kullanılarak cihazın yönünü (azimut, eğim, yuvarlanma) temsil eden açılara dönüştürülür.Kabe'ye Olan Yönü HesaplamaPusulanın mantıksal çekirdeği, coğrafi bir hesaplamadır.Sabit Koordinatlar: Kabe'nin konumu sabittir (Enlem: 21.4224779, Boylam: 39.8251832).71Yön Hesaplama: Kullanıcının mevcut konumu (Bölüm 5'ten alınır) ve Kabe'nin konumu kullanılarak, iki nokta arasındaki en kısa yolu (büyük daire mesafesi) temel alan yönlendirme formülü ile Gerçek Kuzey'e göre Kıble açısı hesaplanır.71Kıble'ye İşaret Eden Dinamik Bir Pusula Arayüzü OluşturmaArayüz, iki temel bilgiyi birleştirerek çalışır:Cihazın sensörlerden gelen mevcut yönü (azimut).Hesaplanan Kıble açısı.Arayüzde, cihazın yönüne göre dönen bir pusula resmi bulunur. Bu pusulanın üzerinde, Kıble yönünü gösteren ayrı bir iğne veya işaretçi yer alır.74 İğnenin son açısı, genellikle cihazın azimutunu Kıble açısından çıkararak bulunur.Sensör verileri doğal olarak gürültülüdür ve anlık dalgalanmalar gösterebilir. Ham azimut değerini doğrudan pusula arayüzünü döndürmek için kullanmak, titrek ve rahatsız edici bir kullanıcı deneyimine yol açar. Profesyonel bir çözüm, sensör verilerine bir alçak geçiren filtre (low-pass filter) uygulamaktır.68 Bu basit sinyal işleme tekniği, mevcut okumayı önceki okumalarla ortalamasını alarak veriyi yumuşatır. Sonuç olarak, pusula animasyonu akıcı ve kararlı hale gelir. Bu, basit bir uygulamayı cilalı ve profesyonel hissettiren ince ama çok önemli bir ayrıntıdır.Bölüm 8: Son Adım - Google Play Store'da YayınlamaUygulama tamamlandığında, son adım onu dünya çapındaki kullanıcılara ulaştırmaktır.Google Play Geliştirici Hesabınızı OluşturmaKayıt: Google Play Console'a gidin ve bir Google hesabıyla kaydolun.76Kayıt Ücreti: Tek seferlik 25 ABD Doları tutarında bir kayıt ücreti bulunmaktadır.76Hesap Türü: Kişisel (Personal) veya Kurumsal (Organization) hesap türlerinden birini seçin.77Kimlik Doğrulaması: Google, tüm yeni geliştiricilerin kimliğini doğrulamalarını zorunlu kılar. Bu işlem birkaç gün sürebilir.76Uygulamanızı Yayına Hazırlama (İkonlar, Ekran Görüntüleri, Politikalar)Mağaza girişi, uygulamanızın vitrinidir. Aşağıdaki varlıkların hazırlanması gerekir:Uygulama İkonu: Yüksek çözünürlüklü ve farklı boyutlarda.Özellik Grafiği: Mağaza girişinin en üstünde görünen banner.Ekran Görüntüleri: Uygulamanın temel özelliklerini gösteren, farklı cihaz boyutları için hazırlanmış görseller.Açıklamalar: Uygulamayı anlatan kısa ve uzun metinler.Gizlilik Politikası: Uygulamanın hangi verileri topladığını ve nasıl kullandığını açıklayan bir gizlilik politikası oluşturmak ve mağaza girişinde bu politikaya bir bağlantı sağlamak zorunludur.Yayına Hazır Bir Uygulama Paketi Oluşturma ve İmzalamaAndroid App Bundle (.aab): Uygulamaları yayınlamak için modern ve standart formattır.77İmzalama Anahtarı: Android Studio kullanılarak özel bir imzalama anahtarı oluşturulur. Bu anahtar, geliştiricinin kimliğinin kriptografik kanıtıdır ve güvenli bir şekilde yedeklenmelidir.Paketi Oluşturma: Android Studio'nun "Generate Signed Bundle or APK" sihirbazı kullanılarak son yayın dosyası oluşturulur.Uygulamanızı Göndermek ve Yayınlamak için Play Console'da GezinmePlay Console paneli üzerinden uygulamanın mağaza girişiyle ilgili tüm gerekli bölümler doldurulur: Uygulama içeriği (hedef kitle, veri güvenliği), fiyatlandırma ve dağıtım. İmzalanmış .aab dosyası bir yayın kanalına (örneğin, Dahili Test, Kapalı Test veya Üretim) yüklenir ve Google'ın incelemesine gönderilir.77Google Play'in konum erişimiyle ilgili politikaları oldukça katıdır. Bu uygulama hem ön plan konumunu (pusula için) hem de arka plan işlevselliğini (alarmlar için) kullandığından, izin isteme gerekçeleri ve Play Console'daki Veri Güvenliği bölümü titizlikle doldurulmalıdır. Aksi takdirde, uygulamanın reddedilmesi yaygın bir durumdur. İzin diyalogundan önce kullanıcıya, konumun neden gerekli olduğunu ("Doğru namaz vakitlerini hesaplamak için konumunuza ihtiyacımız var") açıklayan net bir gerekçe sunulmalıdır. Veri Güvenliği formunda, konum verilerinin toplandığı, nedeninin "Uygulama İşlevselliği" olduğu ve üçüncü taraflarla paylaşılmadığı açıkça belirtilmelidir. Bu proaktif yaklaşım, inceleme sürecinin sorunsuz geçme olasılığını artırır.SonuçBu yol haritası, programlama bilgisi olmayan bir girişimcinin, yapay zeka destekli araçlar kullanarak modern, sağlam ve özellik zengini bir Android namaz vakti uygulaması oluşturması için kapsamlı bir rehber sunmaktadır. Kotlin, Jetpack Compose, ViewModel ve DataStore gibi modern teknolojilerin seçimi, yalnızca teknik bir tercih değil, aynı zamanda geliştirme sürecini basitleştiren, hata olasılığını azaltan ve nihayetinde başarı şansını artıran stratejik bir karardır.Tek Yönlü Veri Akışı gibi sağlam mimari kalıplarını benimsemek, "önce önbelleğe bak" gibi verimli veri stratejileri uygulamak ve kullanıcı deneyimini iyileştiren (yumuşatılmış sensör verileri veya anlaşılır ayarlar gibi) ince ayrıntılara dikkat etmek, ortaya çıkan ürünün kalitesini önemli ölçüde yükseltecektir. Bu prensipleri takip ederek, bir konsepti Google Play Console'da yayınlanmış, milyonlarca kullanıcıya hizmet vermeye hazır, yüksek kaliteli bir uygulamaya dönüştürmek tamamen mümkündür.
