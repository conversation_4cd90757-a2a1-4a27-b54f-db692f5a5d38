<!DOCTYPE html>
<html lang="tr"><head>
<meta charset="utf-8"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Spline+Sans%3Awght%40400%3B500%3B700" onload="this.rel='stylesheet'" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet"/>
<title>Stitch Design</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style type="text/tailwindcss">
      :root {
        --primary-color: #38e07b;
      }
      .material-symbols-outlined {
        font-variation-settings:
        'FILL' 1,
        'wght' 400,
        'GRAD' 0,
        'opsz' 24
      }
    </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-[#111714]">
<div class="relative flex size-full min-h-screen flex-col dark justify-between group/design-root overflow-x-hidden" style='font-family: "Spline Sans", "Noto Sans", sans-serif;'>
<div class="flex flex-col">
<header class="flex items-center p-4 pb-2 justify-between sticky top-0 bg-[#111714]/80 backdrop-blur-sm z-10">
<button class="text-white flex size-10 shrink-0 items-center justify-center rounded-full hover:bg-white/10 transition-colors">
<span class="material-symbols-outlined text-2xl">arrow_back_ios_new</span>
</button>
<h1 class="text-white text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pr-10">Ayarlar</h1>
</header>
<main class="flex-1 overflow-y-auto px-4 pt-4">
<section class="mb-8">
<h2 class="text-white text-xl font-bold leading-tight tracking-[-0.015em] pb-3">Namaz Vakitleri</h2>
<div class="bg-[#1C2620] rounded-xl">
<div class="flex items-center justify-between gap-4 p-4 border-b border-white/10">
<div class="flex flex-col">
<p class="text-white text-base font-medium leading-normal">Bildirimler</p>
<p class="text-[#9eb7a8] text-sm font-normal leading-normal">Bildirimleri aç/kapa</p>
</div>
<label class="relative flex h-[31px] w-[51px] cursor-pointer items-center rounded-full bg-[#29382f] p-0.5 transition-colors has-[:checked]:bg-[var(--primary-color)] has-[:checked]:justify-end">
<div class="h-full w-[27px] rounded-full bg-white transition-transform" style="box-shadow: rgba(0, 0, 0, 0.15) 0px 3px 8px, rgba(0, 0, 0, 0.06) 0px 3px 1px;"></div>
<input checked="" class="invisible absolute" type="checkbox"/>
</label>
</div>
<a class="flex items-center justify-between gap-4 p-4 border-b border-white/10 hover:bg-white/5 transition-colors" href="#">
<div class="flex flex-col">
<p class="text-white text-base font-medium leading-normal">Bildirim Sesi</p>
<p class="text-[#9eb7a8] text-sm font-normal leading-normal">Bildirim sesini değiştir</p>
</div>
<span class="material-symbols-outlined text-white/50 text-2xl">arrow_forward_ios</span>
</a>
<div class="flex items-center justify-between gap-4 p-4">
<div class="flex flex-col">
<p class="text-white text-base font-medium leading-normal">Sessiz Mod</p>
<p class="text-[#9eb7a8] text-sm font-normal leading-normal">Bildirimleri sessize al</p>
</div>
<label class="relative flex h-[31px] w-[51px] cursor-pointer items-center rounded-full bg-[#29382f] p-0.5 transition-colors has-[:checked]:bg-[var(--primary-color)] has-[:checked]:justify-end">
<div class="h-full w-[27px] rounded-full bg-white transition-transform" style="box-shadow: rgba(0, 0, 0, 0.15) 0px 3px 8px, rgba(0, 0, 0, 0.06) 0px 3px 1px;"></div>
<input class="invisible absolute" type="checkbox"/>
</label>
</div>
</div>
</section>
<section class="mb-8">
<h2 class="text-white text-xl font-bold leading-tight tracking-[-0.015em] pb-3">Kaza Namazları</h2>
<div class="bg-[#1C2620] rounded-xl">
<div class="flex items-center justify-between gap-4 p-4 border-b border-white/10">
<div class="flex flex-col">
<p class="text-white text-base font-medium leading-normal">Hatırlatıcılar</p>
<p class="text-[#9eb7a8] text-sm font-normal leading-normal">Kaza namazları için hatırlatıcılar</p>
</div>
<label class="relative flex h-[31px] w-[51px] cursor-pointer items-center rounded-full bg-[#29382f] p-0.5 transition-colors has-[:checked]:bg-[var(--primary-color)] has-[:checked]:justify-end">
<div class="h-full w-[27px] rounded-full bg-white transition-transform" style="box-shadow: rgba(0, 0, 0, 0.15) 0px 3px 8px, rgba(0, 0, 0, 0.06) 0px 3px 1px;"></div>
<input checked="" class="invisible absolute" type="checkbox"/>
</label>
</div>
<a class="flex items-center justify-between gap-4 p-4 hover:bg-white/5 transition-colors" href="#">
<div class="flex flex-col">
<p class="text-white text-base font-medium leading-normal">Hatırlatıcı Zamanı</p>
<p class="text-[#9eb7a8] text-sm font-normal leading-normal">Hatırlatıcı zamanını değiştir</p>
</div>
<span class="material-symbols-outlined text-white/50 text-2xl">arrow_forward_ios</span>
</a>
</div>
</section>
<section>
<h2 class="text-white text-xl font-bold leading-tight tracking-[-0.015em] pb-3">Genel</h2>
<div class="bg-[#1C2620] rounded-xl">
<a class="flex items-center justify-between gap-4 p-4 border-b border-white/10 hover:bg-white/5 transition-colors" href="#">
<div class="flex flex-col">
<p class="text-white text-base font-medium leading-normal">Tema</p>
<p class="text-[#9eb7a8] text-sm font-normal leading-normal">Uygulama temasını değiştir</p>
</div>
<span class="material-symbols-outlined text-white/50 text-2xl">arrow_forward_ios</span>
</a>
<a class="flex items-center justify-between gap-4 p-4 hover:bg-white/5 transition-colors" href="#">
<div class="flex flex-col">
<p class="text-white text-base font-medium leading-normal">Dil</p>
<p class="text-[#9eb7a8] text-sm font-normal leading-normal">Uygulama dilini değiştir</p>
</div>
<span class="material-symbols-outlined text-white/50 text-2xl">arrow_forward_ios</span>
</a>
</div>
</section>
</main>
</div>
<footer class="sticky bottom-0">
<div class="bg-[#1c2620]/80 backdrop-blur-sm pt-2 pb-5 px-2 flex justify-around">
<a class="flex flex-col items-center justify-end gap-1 text-[#9eb7a8] transition-colors hover:text-white" href="#">
<span class="material-symbols-outlined text-2xl">schedule</span>
<p class="text-xs font-medium tracking-[0.015em]">Vakitler</p>
</a>
<a class="flex flex-col items-center justify-end gap-1 text-[#9eb7a8] transition-colors hover:text-white" href="#">
<span class="material-symbols-outlined text-2xl">checklist</span>
<p class="text-xs font-medium tracking-[0.015em]">Kaza</p>
</a>
<a class="flex flex-col items-center justify-end gap-1 text-[#9eb7a8] transition-colors hover:text-white" href="#">
<span class="material-symbols-outlined text-2xl">explore</span>
<p class="text-xs font-medium tracking-[0.015em]">Kıble</p>
</a>
<a class="flex flex-col items-center justify-end gap-1 text-[var(--primary-color)]" href="#">
<span class="material-symbols-outlined text-2xl">settings</span>
<p class="text-xs font-medium tracking-[0.015em]">Ayarlar</p>
</a>
<a class="flex flex-col items-center justify-end gap-1 text-[#9eb7a8] transition-colors hover:text-white" href="#">
<span class="material-symbols-outlined text-2xl">calculate</span>
<p class="text-xs font-medium tracking-[0.015em] text-center">Kaza Hesapla</p>
</a>
</div>
</footer>
</div>
</body></html>