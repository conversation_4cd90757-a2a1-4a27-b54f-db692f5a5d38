<html><head>
<meta charset="utf-8"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Spline+Sans%3Awght%40400%3B500%3B700" onload="this.rel='stylesheet'" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet"/>
<title>Stitch Design</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style type="text/tailwindcss">
    :root {
      --primary: #38e07b;
      --background: #111714;
      --foreground: #ffffff;
      --secondary-foreground: #9eb7a8;
      --border: #3d5245;
      --input: #1c2620;
    }
    .custom-radio:checked+label {
      background-color: var(--primary);
      color: var(--background);
      border-color: var(--primary);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-[var(--background)]" style='font-family: "Spline Sans", "Noto Sans", sans-serif;'>
<div class="relative flex size-full min-h-screen flex-col group/design-root overflow-x-hidden">
<div class="flex-grow">
<header class="flex items-center p-4">
<button class="text-[var(--foreground)]">
<span class="material-symbols-outlined text-3xl">close</span>
</button>
<h1 class="text-[var(--foreground)] text-xl font-bold leading-tight tracking-[-0.015em] flex-1 text-center pr-8">
          Kaza Ekle
        </h1>
</header>
<main class="p-4 space-y-8">
<section>
<h2 class="text-[var(--foreground)] text-lg font-bold leading-tight tracking-[-0.015em] mb-4">Namaz Vakti</h2>
<div class="grid grid-cols-3 gap-3">
<input class="invisible absolute custom-radio" id="sabah" name="namaz_vakti" type="radio"/>
<label class="text-sm font-medium text-center rounded-full border border-[var(--border)] px-4 py-3 text-[var(--foreground)] cursor-pointer transition-colors duration-200" for="sabah">Sabah</label>
<input class="invisible absolute custom-radio" id="ogle" name="namaz_vakti" type="radio"/>
<label class="text-sm font-medium text-center rounded-full border border-[var(--border)] px-4 py-3 text-[var(--foreground)] cursor-pointer transition-colors duration-200" for="ogle">Öğle</label>
<input class="invisible absolute custom-radio" id="ikindi" name="namaz_vakti" type="radio"/>
<label class="text-sm font-medium text-center rounded-full border border-[var(--border)] px-4 py-3 text-[var(--foreground)] cursor-pointer transition-colors duration-200" for="ikindi">İkindi</label>
<input class="invisible absolute custom-radio" id="aksam" name="namaz_vakti" type="radio"/>
<label class="text-sm font-medium text-center rounded-full border border-[var(--border)] px-4 py-3 text-[var(--foreground)] cursor-pointer transition-colors duration-200" for="aksam">Akşam</label>
<input class="invisible absolute custom-radio" id="yatsi" name="namaz_vakti" type="radio"/>
<label class="text-sm font-medium text-center rounded-full border border-[var(--border)] px-4 py-3 text-[var(--foreground)] cursor-pointer transition-colors duration-200" for="yatsi">Yatsı</label>
</div>
</section>
<section>
<h2 class="text-[var(--foreground)] text-lg font-bold leading-tight tracking-[-0.015em] mb-4">Tarih</h2>
<div class="relative">
<input class="form-input w-full resize-none overflow-hidden rounded-full text-[var(--foreground)] focus:outline-none focus:ring-2 focus:ring-[var(--primary)] border border-[var(--border)] bg-[var(--input)] h-14 placeholder:text-[var(--secondary-foreground)] px-6 text-base font-normal" placeholder="Tarih seçin" type="text"/>
<div class="absolute right-4 top-1/2 -translate-y-1/2 text-[var(--secondary-foreground)]" data-icon="Calendar" data-size="24px" data-weight="regular">
<span class="material-symbols-outlined text-2xl"> calendar_today </span>
</div>
</div>
</section>
</main>
</div>
<div class="sticky bottom-0">
<div class="px-4 pb-4">
<button class="flex w-full cursor-pointer items-center justify-center overflow-hidden rounded-full h-14 px-5 bg-[var(--primary)] text-[var(--background)] text-base font-bold leading-normal tracking-[0.015em] hover:bg-opacity-90 transition-opacity">
<span class="truncate">Ekle</span>
</button>
</div>
<nav class="bg-black/50 backdrop-blur-lg border-t border-[var(--border)]">
<div class="flex justify-around items-center h-20">
<a class="flex flex-col items-center gap-1 text-[var(--secondary-foreground)] hover:text-[var(--primary)] transition-colors" href="#">
<span class="material-symbols-outlined">schedule</span>
<span class="text-xs font-medium">Vakitler</span>
</a>
<a class="flex flex-col items-center gap-1 text-[var(--secondary-foreground)] hover:text-[var(--primary)] transition-colors text-[var(--primary)]" href="#">
<span class="material-symbols-outlined">checklist</span>
<span class="text-xs font-medium">Kaza</span>
</a>
<a class="flex flex-col items-center gap-1 text-[var(--secondary-foreground)] hover:text-[var(--primary)] transition-colors" href="#">
<span class="material-symbols-outlined">explore</span>
<span class="text-xs font-medium">Kıble</span>
</a>
<a class="flex flex-col items-center gap-1 text-[var(--secondary-foreground)] hover:text-[var(--primary)] transition-colors" href="#">
<span class="material-symbols-outlined">settings</span>
<span class="text-xs font-medium">Ayarlar</span>
</a>
<a class="flex flex-col items-center gap-1 text-[var(--secondary-foreground)] hover:text-[var(--primary)] transition-colors" href="#">
<span class="material-symbols-outlined">calculate</span>
<span class="text-xs font-medium">Kaza Hesapla</span>
</a>
</div>
</nav>
</div>
</div>

</body></html>