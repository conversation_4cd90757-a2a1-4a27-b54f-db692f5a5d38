// Konum servisi
// Bu servis, konum ile ilgili tüm işlemleri yönetir:
// - Konum izinlerini yönetme
// - Konum bilgilerini alma
// - Konum takibi
// - Kıble yönü hesaplama
// - Konum servis durumunu kontrol etme

import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../models/location_model.dart';

/// Konum servisi sınıfı
class LocationService {
  /// Servis örneğ<PERSON> (singleton)
  static final LocationService _instance = LocationService._internal();

  /// İç oluşturucu
  LocationService._internal() {
    _initializeLocale();
  }

  /// Locale'i başlatır (Türkçe için)
  Future<void> _initializeLocale() async {
    if (!_localeInitialized) {
      await setLocaleIdentifier('tr_TR');
      _localeInitialized = true;
    }
  }

  bool _localeInitialized = false;

  /// Servis örneğini döndürür
  factory LocationService() => _instance;

  /// Konum akışı
  Stream<LocationModel>? _locationStream;

  /// Konum akışını döndürür
  Stream<LocationModel>? get locationStream => _locationStream;

  /// Konum takibi aktif mi?
  bool _isTracking = false;

  /// Konum takibi aktif mi?
  bool get isTracking => _isTracking;

  /// Konum servisi durumu akışı
  Stream<LocationServiceStatus>? _serviceStatusStream;

  /// Konum servisi durum akışını döndürür
  Stream<LocationServiceStatus>? get serviceStatusStream =>
      _serviceStatusStream;

  /// Konum izin durumu
  LocationPermissionStatus? _permissionStatus;

  /// Konum izin durumunu döndürür
  LocationPermissionStatus? get permissionStatus => _permissionStatus;

  /// Konum servis durumunu kontrol eder
  Future<LocationServiceStatus> checkServiceStatus() async {
    try {
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (serviceEnabled) {
        return LocationServiceStatus.enabled;
      } else {
        return LocationServiceStatus.disabled;
      }
    } catch (e) {
      return LocationServiceStatus.unknown;
    }
  }

  /// Konum izin durumunu kontrol eder
  Future<LocationPermissionStatus> checkPermissionStatus() async {
    try {
      final status = await Geolocator.checkPermission();

      switch (status) {
        case LocationPermission.always:
        case LocationPermission.whileInUse:
          _permissionStatus = LocationPermissionStatus.granted;
          return LocationPermissionStatus.granted;

        case LocationPermission.denied:
          _permissionStatus = LocationPermissionStatus.denied;
          return LocationPermissionStatus.denied;

        case LocationPermission.deniedForever:
          _permissionStatus = LocationPermissionStatus.permanentlyDenied;
          return LocationPermissionStatus.permanentlyDenied;

        case LocationPermission.unableToDetermine:
          _permissionStatus = LocationPermissionStatus.notDetermined;
          return LocationPermissionStatus.notDetermined;
      }
    } catch (e) {
      return LocationPermissionStatus.notDetermined;
    }
  }

  /// Konum izni ister
  Future<LocationPermissionStatus> requestPermission() async {
    try {
      final status = await Geolocator.requestPermission();

      switch (status) {
        case LocationPermission.always:
        case LocationPermission.whileInUse:
          _permissionStatus = LocationPermissionStatus.granted;
          return LocationPermissionStatus.granted;

        case LocationPermission.denied:
          _permissionStatus = LocationPermissionStatus.denied;
          return LocationPermissionStatus.denied;

        case LocationPermission.deniedForever:
          _permissionStatus = LocationPermissionStatus.permanentlyDenied;
          return LocationPermissionStatus.permanentlyDenied;

        case LocationPermission.unableToDetermine:
          _permissionStatus = LocationPermissionStatus.notDetermined;
          return LocationPermissionStatus.notDetermined;
      }
    } catch (e) {
      return LocationPermissionStatus.notDetermined;
    }
  }

  /// Konum takibini başlatır
  Future<bool> startLocationTracking({
    LocationSettings? locationSettings,
    LocationAccuracy desiredAccuracy = LocationAccuracy.high,
    double distanceFilter = 100,
  }) async {
    try {
      // İzin durumunu kontrol et
      final permissionStatus = await checkPermissionStatus();
      if (permissionStatus != LocationPermissionStatus.granted) {
        final requestedStatus = await requestPermission();
        if (requestedStatus != LocationPermissionStatus.granted) {
          return false;
        }
      }

      // Servis durumunu kontrol et
      final serviceStatus = await checkServiceStatus();
      if (serviceStatus != LocationServiceStatus.enabled) {
        return false;
      }

      // Mevcut akışı kapat
      stopLocationTracking();

      // Backward compatibility için settings oluştur
      final settings =
          locationSettings ??
          _createLocationSettings(
            accuracy: desiredAccuracy,
            distanceFilter: distanceFilter,
          );

      // Yeni akışı başlat
      _locationStream = Geolocator.getPositionStream(locationSettings: settings)
          .map((position) {
            return LocationModel(
              latitude: position.latitude,
              longitude: position.longitude,
              accuracy: position.accuracy,
              timestamp: position.timestamp,
              status: _getLocationStatus(position),
            );
          });

      _isTracking = true;
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Konum takibini durdurur
  void stopLocationTracking() {
    _locationStream = null;
    _isTracking = false;
  }

  /// Tek seferlik konum bilgisi alır
  Future<LocationModel?> getCurrentLocation({
    LocationSettings? locationSettings,
    LocationAccuracy desiredAccuracy = LocationAccuracy.high,
    bool forceLocationManager = false,
  }) async {
    try {
      // İzin durumunu kontrol et
      final permissionStatus = await checkPermissionStatus();
      if (permissionStatus != LocationPermissionStatus.granted) {
        final requestedStatus = await requestPermission();
        if (requestedStatus != LocationPermissionStatus.granted) {
          return null;
        }
      }

      // Servis durumunu kontrol et
      final serviceStatus = await checkServiceStatus();
      if (serviceStatus != LocationServiceStatus.enabled) {
        return null;
      }

      // Backward compatibility için settings oluştur
      final settings =
          locationSettings ??
          _createLocationSettings(
            accuracy: desiredAccuracy,
            forceLocationManager: forceLocationManager,
          );

      // Konum bilgisini al
      final position = await Geolocator.getCurrentPosition(
        locationSettings: settings,
      );

      return LocationModel(
        latitude: position.latitude,
        longitude: position.longitude,
        accuracy: position.accuracy,
        timestamp: position.timestamp,
        status: _getLocationStatus(position),
      );
    } catch (e) {
      return null;
    }
  }

  /// Tam konum adresini alır (reverse geocoding)
  Future<String?> getFullAddress(double latitude, double longitude) async {
    try {
      // Locale'i başlat
      await _initializeLocale();

      final placemarks = await placemarkFromCoordinates(latitude, longitude);
      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        // Boş olmayan parçaları birleştir
        final parts =
            <String>[
                  placemark.thoroughfare,
                  placemark.subLocality,
                  placemark.locality,
                  placemark.administrativeArea,
                  placemark.country,
                ]
                .where((part) => part != null && part.isNotEmpty)
                .map((part) => part!)
                .toList();
        return parts.join(', ');
      }
      // Fallback: Koordinat string'i
      return '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';
    } catch (e) {
      debugPrint('Adres alınamadı: $e');
      // Fallback
      return '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';
    }
  }

  /// Eski isim için wrapper (geriye uyumluluk)
  @deprecated
  Future<String?> getLocationName(double latitude, double longitude) async {
    return await getFullAddress(latitude, longitude);
  }

  /// Adresten koordinat alır (forward geocoding)
  Future<LocationModel?> getLocationFromAddress(String address) async {
    try {
      final locations = await locationFromAddress(address);
      if (locations.isNotEmpty) {
        final location = locations.first;
        return LocationModel(
          latitude: location.latitude,
          longitude: location.longitude,
          accuracy: 0, // Forward geocoding'de doğruluk bilgisi yok
          timestamp: DateTime.now(),
          status: LocationStatus.network,
        );
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Kıble yönünü hesaplar
  Future<double> calculateQiblaDirection(
    double latitude,
    double longitude,
  ) async {
    // Kabe'nin koordinatları (Mekke)
    const double kaabaLatitude = 21.4225;
    const double kaabaLongitude = 39.8262;

    // Kıble yönü hesaplama formülü
    final double deltaLongitude = kaabaLongitude - longitude;

    final double y =
        sin(deltaLongitude * pi / 180) * cos(kaabaLatitude * pi / 180);
    final double x =
        cos(latitude * pi / 180) * sin(kaabaLatitude * pi / 180) -
        sin(latitude * pi / 180) *
            cos(kaabaLatitude * pi / 180) *
            cos(deltaLongitude * pi / 180);

    double qiblaDirection = atan2(y, x) * 180 / pi;
    qiblaDirection = (qiblaDirection + 360) % 360; // Pozitif dereceye çevir

    return qiblaDirection;
  }

  /// Konum durumunu belirler
  LocationStatus _getLocationStatus(Position position) {
    if (position.accuracy < 50) {
      return LocationStatus.gps;
    } else if (position.accuracy < 500) {
      return LocationStatus.network;
    } else {
      return LocationStatus.passive;
    }
  }

  /// Son bilinen konumu alır
  /// Geolocator.getLastKnownPosition() ile cihazın son bilinen konumunu alır.
  /// Konum mevcutsa LocationModel'e dönüştürür, yoksa null döner.
  /// İzin kontrolü yapmaz, sadece mevcut veriyi kullanır.
  /// Hata durumlarında null döner.
  Future<LocationModel?> getLastKnownLocation() async {
    try {
      final position = await Geolocator.getLastKnownPosition();
      if (position != null) {
        return LocationModel(
          latitude: position.latitude,
          longitude: position.longitude,
          accuracy: position.accuracy,
          timestamp: position.timestamp,
          status: _getLocationStatus(position),
        );
      }
      return null;
    } catch (e) {
      // Hata durumunda null dön (örneğin platform farkları veya erişim sorunları)
      return null;
    }
  }

  /// Platform-specific LocationSettings oluşturur
  /// Bu fonksiyon, mevcut parametreleri modern LocationSettings yapısına dönüştürür
  LocationSettings _createLocationSettings({
    LocationAccuracy accuracy = LocationAccuracy.high,
    double distanceFilter = 100,
    bool forceLocationManager = false,
  }) {
    if (defaultTargetPlatform == TargetPlatform.android) {
      return AndroidSettings(
        accuracy: accuracy,
        distanceFilter: distanceFilter.toInt(),
        forceLocationManager: forceLocationManager,
        intervalDuration: const Duration(seconds: 10),
        // Arka planda konum takibi için foreground notification (opsiyonel)
        // foregroundNotificationConfig: const ForegroundNotificationConfig(
        //   notificationText: "Konum servisi arka planda çalışıyor",
        //   notificationTitle: "Namaz Vakitleri",
        //   enableWakeLock: true,
        // ),
      );
    } else if (defaultTargetPlatform == TargetPlatform.iOS ||
        defaultTargetPlatform == TargetPlatform.macOS) {
      return AppleSettings(
        accuracy: accuracy,
        activityType: ActivityType.fitness,
        distanceFilter: distanceFilter.toInt(),
        pauseLocationUpdatesAutomatically: true,
        // Arka plan konum göstergesi (iOS 16+)
        showBackgroundLocationIndicator: false,
      );
    } else if (kIsWeb) {
      return WebSettings(
        accuracy: accuracy,
        distanceFilter: distanceFilter.toInt(),
        maximumAge: const Duration(minutes: 5),
      );
    } else {
      // Diğer platformlar için genel ayarlar
      return LocationSettings(
        accuracy: accuracy,
        distanceFilter: distanceFilter.toInt(),
      );
    }
  }
}
