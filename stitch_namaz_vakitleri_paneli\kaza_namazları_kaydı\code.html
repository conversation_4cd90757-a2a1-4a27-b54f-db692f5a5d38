<!DOCTYPE html>
<html><head>
<meta charset="utf-8"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Spline+Sans%3Awght%40400%3B500%3B700" onload="this.rel='stylesheet'" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet"/>
<title>Stitch Design</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style type="text/tailwindcss">
      :root {
        --primary-color: #38e07b;
      }
    </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-[#111714]">
<div class="relative flex size-full min-h-screen flex-col dark justify-between group/design-root overflow-x-hidden" style='font-family: "Spline Sans", "Noto Sans", sans-serif;'>
<div class="flex-grow">
<header class="sticky top-0 z-10 flex items-center justify-between bg-[#111714]/80 p-4 backdrop-blur-sm">
<button class="text-white">
<span class="material-symbols-outlined"> arrow_back_ios_new </span>
</button>
<h1 class="text-xl font-bold text-white">Kaza Namazları</h1>
<div class="w-8"></div>
</header>
<main class="px-4 py-6">
<div class="space-y-8">
<div class="rounded-2xl bg-[#1C2620] p-4">
<h2 class="mb-4 text-xl font-bold text-[var(--primary-color)]">Sabah</h2>
<div class="space-y-4">
<div class="flex items-center justify-between">
<div>
<p class="text-base font-medium text-white">Kılınan</p>
<p class="text-2xl font-bold text-white">10</p>
</div>
<button class="flex h-10 w-10 cursor-pointer items-center justify-center rounded-full bg-[var(--primary-color)] text-[#111714]">
<span class="material-symbols-outlined"> add </span>
</button>
</div>
<div class="flex items-center justify-between">
<div>
<p class="text-base font-medium text-white">Kalan</p>
<p class="text-2xl font-bold text-white">5</p>
</div>
<button class="flex h-10 w-10 cursor-pointer items-center justify-center rounded-full bg-[#29382F] text-white">
<span class="material-symbols-outlined"> edit </span>
</button>
</div>
</div>
</div>
<div class="rounded-2xl bg-[#1C2620] p-4">
<h2 class="mb-4 text-xl font-bold text-[var(--primary-color)]">Öğle</h2>
<div class="space-y-4">
<div class="flex items-center justify-between">
<div>
<p class="text-base font-medium text-white">Kılınan</p>
<p class="text-2xl font-bold text-white">8</p>
</div>
<button class="flex h-10 w-10 cursor-pointer items-center justify-center rounded-full bg-[var(--primary-color)] text-[#111714]">
<span class="material-symbols-outlined"> add </span>
</button>
</div>
<div class="flex items-center justify-between">
<div>
<p class="text-base font-medium text-white">Kalan</p>
<p class="text-2xl font-bold text-white">3</p>
</div>
<button class="flex h-10 w-10 cursor-pointer items-center justify-center rounded-full bg-[#29382F] text-white">
<span class="material-symbols-outlined"> edit </span>
</button>
</div>
</div>
</div>
<div class="rounded-2xl bg-[#1C2620] p-4">
<h2 class="mb-4 text-xl font-bold text-[var(--primary-color)]">İkindi</h2>
<div class="space-y-4">
<div class="flex items-center justify-between">
<div>
<p class="text-base font-medium text-white">Kılınan</p>
<p class="text-2xl font-bold text-white">12</p>
</div>
<button class="flex h-10 w-10 cursor-pointer items-center justify-center rounded-full bg-[var(--primary-color)] text-[#111714]">
<span class="material-symbols-outlined"> add </span>
</button>
</div>
<div class="flex items-center justify-between">
<div>
<p class="text-base font-medium text-white">Kalan</p>
<p class="text-2xl font-bold text-white">7</p>
</div>
<button class="flex h-10 w-10 cursor-pointer items-center justify-center rounded-full bg-[#29382F] text-white">
<span class="material-symbols-outlined"> edit </span>
</button>
</div>
</div>
</div>
<div class="rounded-2xl bg-[#1C2620] p-4">
<h2 class="mb-4 text-xl font-bold text-[var(--primary-color)]">Akşam</h2>
<div class="space-y-4">
<div class="flex items-center justify-between">
<div>
<p class="text-base font-medium text-white">Kılınan</p>
<p class="text-2xl font-bold text-white">6</p>
</div>
<button class="flex h-10 w-10 cursor-pointer items-center justify-center rounded-full bg-[var(--primary-color)] text-[#111714]">
<span class="material-symbols-outlined"> add </span>
</button>
</div>
<div class="flex items-center justify-between">
<div>
<p class="text-base font-medium text-white">Kalan</p>
<p class="text-2xl font-bold text-white">2</p>
</div>
<button class="flex h-10 w-10 cursor-pointer items-center justify-center rounded-full bg-[#29382F] text-white">
<span class="material-symbols-outlined"> edit </span>
</button>
</div>
</div>
</div>
<div class="rounded-2xl bg-[#1C2620] p-4">
<h2 class="mb-4 text-xl font-bold text-[var(--primary-color)]">Yatsı</h2>
<div class="space-y-4">
<div class="flex items-center justify-between">
<div>
<p class="text-base font-medium text-white">Kılınan</p>
<p class="text-2xl font-bold text-white">9</p>
</div>
<button class="flex h-10 w-10 cursor-pointer items-center justify-center rounded-full bg-[var(--primary-color)] text-[#111714]">
<span class="material-symbols-outlined"> add </span>
</button>
</div>
<div class="flex items-center justify-between">
<div>
<p class="text-base font-medium text-white">Kalan</p>
<p class="text-2xl font-bold text-white">4</p>
</div>
<button class="flex h-10 w-10 cursor-pointer items-center justify-center rounded-full bg-[#29382F] text-white">
<span class="material-symbols-outlined"> edit </span>
</button>
</div>
</div>
</div>
</div>
</main>
</div>
<footer class="sticky bottom-0 border-t border-[#29382f] bg-[#1c2620]/80 pb-safe backdrop-blur-sm">
<nav class="flex justify-around px-2 pb-3 pt-2">
<a class="flex flex-1 flex-col items-center justify-end gap-1 text-[#9eb7a8]" href="#">
<span class="material-symbols-outlined text-2xl"> schedule </span>
<p class="text-xs font-medium tracking-[0.015em]">Vakitler</p>
</a>
<a class="flex flex-1 flex-col items-center justify-end gap-1 rounded-full text-[var(--primary-color)]" href="#">
<span class="material-symbols-outlined text-2xl"> view_list </span>
<p class="text-xs font-medium tracking-[0.015em]">Kaza</p>
</a>
<a class="flex flex-1 flex-col items-center justify-end gap-1 text-[#9eb7a8]" href="#">
<span class="material-symbols-outlined text-2xl"> explore </span>
<p class="text-xs font-medium leading-normal tracking-[0.015em]">Kıble</p>
</a>
<a class="flex flex-1 flex-col items-center justify-end gap-1 text-[#9eb7a8]" href="#">
<span class="material-symbols-outlined text-2xl"> settings </span>
<p class="text-xs font-medium leading-normal tracking-[0.015em]">Ayarlar</p>
</a>
<a class="flex flex-1 flex-col items-center justify-end gap-1 text-[#9eb7a8]" href="#">
<span class="material-symbols-outlined text-2xl"> calculate </span>
<p class="text-xs font-medium leading-normal tracking-[0.015em]">Kaza Hesapla</p>
</a>
</nav>
</footer>
</div>

</body></html>