// Konum modülü için veri modelleri
// <PERSON>u dosya, konum ile ilgili tüm veri yapılarını içerir

import 'dart:core';
import 'dart:math';

/// Konum bilgilerini temsil eden model sınıfı
class LocationModel {
  /// Enlem (latitude) değeri
  final double latitude;

  /// Boylam (longitude) değeri
  final double longitude;

  /// Konum hassasiyeti (accuracy)
  final double accuracy;

  /// Konum alındığı zaman damgası
  final DateTime timestamp;

  /// Konum durumu (GPS, ağ vb.)
  final LocationStatus status;

  /// Kıble yönü derece cinsinden
  final double? qiblaDirection;

  /// Konum adı (varsa)
  final String? locationName;

  /// Tam konum adresi (il + ilçe + ülke)
  final String? fullAddress;

  /// Konum oluşturucu
  const LocationModel({
    required this.latitude,
    required this.longitude,
    required this.accuracy,
    required this.timestamp,
    required this.status,
    this.qiblaDirection,
    this.locationName,
    this.fullAddress,
  });

  /// JSON'dan LocationModel oluşturma
  factory LocationModel.fromJson(Map<String, dynamic> json) {
    return LocationModel(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      accuracy: (json['accuracy'] as num).toDouble(),
      timestamp: DateTime.parse(json['timestamp']),
      status: LocationStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => LocationStatus.unknown,
      ),
      qiblaDirection: json['qiblaDirection']?.toDouble(),
      locationName: json['locationName'],
      fullAddress: json['fullAddress'],
    );
  }

  /// LocationModel'dan JSON oluşturma
  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'accuracy': accuracy,
      'timestamp': timestamp.toIso8601String(),
      'status': status.name,
      'qiblaDirection': qiblaDirection,
      'locationName': locationName,
      'fullAddress': fullAddress,
    };
  }

  /// Kopya oluşturma metodu
  LocationModel copyWith({
    double? latitude,
    double? longitude,
    double? accuracy,
    DateTime? timestamp,
    LocationStatus? status,
    double? qiblaDirection,
    String? locationName,
    String? fullAddress,
  }) {
    return LocationModel(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      accuracy: accuracy ?? this.accuracy,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
      qiblaDirection: qiblaDirection ?? this.qiblaDirection,
      locationName: locationName ?? this.locationName,
      fullAddress: fullAddress ?? this.fullAddress,
    );
  }

  /// İki konum arasındaki mesafeyi hesaplama (metre cinsinden)
  double distanceTo(LocationModel other) {
    const double earthRadius = 6371000; // Dünya yarıçapı metre cinsinden
    final double dLat = _degreesToRadians(other.latitude - latitude);
    final double dLon = _degreesToRadians(other.longitude - longitude);

    final double a =
        (sin(dLat / 2) * sin(dLat / 2)) +
        (cos(_degreesToRadians(latitude)) *
            cos(_degreesToRadians(other.latitude)) *
            sin(dLon / 2) *
            sin(dLon / 2));
    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  /// Dereceyi radyana çevirme
  double _degreesToRadians(double degrees) {
    return degrees * pi / 180;
  }

  @override
  String toString() {
    return 'LocationModel('
        'latitude: $latitude, '
        'longitude: $longitude, '
        'accuracy: $accuracy, '
        'timestamp: $timestamp, '
        'status: $status, '
        'qiblaDirection: $qiblaDirection, '
        'locationName: $locationName, '
        'fullAddress: $fullAddress)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is LocationModel &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.accuracy == accuracy &&
        other.timestamp == timestamp &&
        other.status == status &&
        other.qiblaDirection == qiblaDirection &&
        other.locationName == locationName &&
        other.fullAddress == fullAddress;
  }

  @override
  int get hashCode {
    return Object.hash(
      latitude,
      longitude,
      accuracy,
      timestamp,
      status,
      qiblaDirection,
      locationName,
      fullAddress,
    );
  }
}

/// Konum durumu enum'u
enum LocationStatus {
  /// GPS ile konum alındı
  gps,

  /// Ağ (WiFi/Cell) ile konum alındı
  network,

  /// Pasif konum takibi
  passive,

  /// Konum alınamadı
  unavailable,

  /// Bilinmeyen durum
  unknown,
}

/// Konum izin durumu enum'u
enum LocationPermissionStatus {
  /// İzin verildi
  granted,

  /// İzin reddedildi
  denied,

  /// İzin henüz istenmedi
  notDetermined,

  /// İzin kalıcı olarak reddedildi
  permanentlyDenied,

  /// İzin kullanılamıyor
  restricted,
}

/// Konum servis durumu enum'u
enum LocationServiceStatus {
  /// Servis aktif
  enabled,

  /// Servis devre dışı
  disabled,

  /// Servis bilinmeyen durumda
  unknown,
}
